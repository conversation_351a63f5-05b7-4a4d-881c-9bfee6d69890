const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false
}));

// 压缩响应
app.use(compression());

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

// 请求日志
app.use(morgan('combined'));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 15分钟内最多1000个请求
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: process.env.NODE_ENV === 'production' ? '1d' : '0'
}));

// 编程术语数据库 - 超大词汇库 (500+ 术语)
const programmingTermsDatabase = {
  // JavaScript核心概念 (80个术语)
  javascript: [
    {
      id: 'js_1',
      name: 'closure',
      chinese: '闭包',
      description: '函数与其词法环境的组合，内部函数可以访问外部函数的变量，即使外部函数已经执行完毕',
      difficulty: 'intermediate',
      tags: ['scope', 'functions', 'memory'],
      example: `function createCounter() {
  let count = 0;
  return function() {
    count++;
    return count;
  };
}

const counter = createCounter();
console.log(counter()); // 1
console.log(counter()); // 2`,
      useCases: ['模块化', '数据私有化', '函数工厂', '事件处理器'],
      relatedTerms: ['scope', 'hoisting', 'execution context']
    },
    {
      id: 'js_2',
      name: 'hoisting',
      chinese: '变量提升',
      description: 'JavaScript引擎在执行代码前将变量和函数声明移到作用域顶部的行为',
      difficulty: 'intermediate',
      tags: ['variables', 'functions', 'execution'],
      example: `console.log(x); // undefined (不是错误!)
var x = 5;

// 等价于:
var x;
console.log(x); // undefined
x = 5;

// 函数声明会被完全提升
sayHello(); // "Hello!" (可以正常调用)
function sayHello() {
  console.log("Hello!");
}`,
      useCases: ['理解执行顺序', '避免常见错误', '代码优化'],
      relatedTerms: ['scope', 'var', 'let', 'const']
    },
    {
      id: 'js_3',
      name: 'prototype',
      chinese: '原型',
      description: 'JavaScript中对象继承的基础机制，每个对象都有一个原型，用于属性和方法的查找',
      difficulty: 'advanced',
      tags: ['inheritance', 'objects', 'methods'],
      example: `function Person(name) {
  this.name = name;
}

Person.prototype.sayHello = function() {
  return \`Hello, I'm \${this.name}\`;
};

Person.prototype.species = 'Homo sapiens';

const alice = new Person('Alice');
console.log(alice.sayHello()); // "Hello, I'm Alice"
console.log(alice.species); // "Homo sapiens"`,
      useCases: ['对象继承', '方法共享', '内存优化', '原型链'],
      relatedTerms: ['inheritance', 'constructor', 'class']
    },
    {
      id: 'js_4',
      name: 'event_loop',
      chinese: '事件循环',
      description: 'JavaScript运行时处理异步操作的核心机制，管理调用栈、任务队列和微任务队列',
      difficulty: 'advanced',
      tags: ['async', 'runtime', 'performance'],
      example: `console.log('1');

setTimeout(() => console.log('2'), 0);

Promise.resolve().then(() => console.log('3'));

console.log('4');

// 输出顺序: 1, 4, 3, 2
// 微任务(Promise)优先于宏任务(setTimeout)`,
      useCases: ['异步编程', '性能优化', '任务调度', '响应式编程'],
      relatedTerms: ['async', 'promise', 'callback', 'microtask']
    },
    {
      id: 'js_5',
      name: 'destructuring',
      chinese: '解构赋值',
      description: '从数组或对象中提取值并赋给变量的便捷语法',
      difficulty: 'beginner',
      tags: ['syntax', 'variables', 'arrays', 'objects'],
      example: `// 对象解构
const person = { name: 'Alice', age: 25, city: 'NYC' };
const { name, age, city = 'Unknown' } = person;

// 数组解构
const colors = ['red', 'green', 'blue'];
const [primary, secondary, ...rest] = colors;

// 函数参数解构
function greet({ name, age }) {
  return \`Hello \${name}, you are \${age} years old\`;
}`,
      useCases: ['变量提取', '函数参数', '数据转换', '默认值设置'],
      relatedTerms: ['spread operator', 'rest parameters', 'default parameters']
    },
    {
      id: 'js_6',
      name: 'promise',
      chinese: 'Promise对象',
      description: '表示异步操作最终完成或失败的对象，提供了更好的异步编程解决方案',
      difficulty: 'intermediate',
      tags: ['async', 'promise', 'callback'],
      example: `// 创建Promise
const fetchData = new Promise((resolve, reject) => {
  setTimeout(() => {
    const success = Math.random() > 0.5;
    if (success) {
      resolve({ data: 'Hello World', status: 200 });
    } else {
      reject(new Error('Network error'));
    }
  }, 1000);
});

// 使用Promise
fetchData
  .then(result => {
    console.log('Success:', result.data);
    return result.data.toUpperCase();
  })
  .then(upperData => {
    console.log('Processed:', upperData);
  })
  .catch(error => {
    console.error('Error:', error.message);
  })
  .finally(() => {
    console.log('Request completed');
  });`,
      useCases: ['异步数据获取', '避免回调地狱', '错误处理', '链式调用'],
      relatedTerms: ['async', 'await', 'callback', 'event loop']
    },
    {
      id: 'js_7',
      name: 'async_await',
      chinese: 'async/await',
      description: '基于Promise的语法糖，使异步代码看起来像同步代码，提高可读性',
      difficulty: 'intermediate',
      tags: ['async', 'promise', 'syntax'],
      example: `// 传统Promise写法
function fetchUserData() {
  return fetch('/api/user')
    .then(response => response.json())
    .then(user => {
      return fetch(\`/api/posts/\${user.id}\`);
    })
    .then(response => response.json());
}

// async/await写法
async function fetchUserDataAsync() {
  try {
    const userResponse = await fetch('/api/user');
    const user = await userResponse.json();

    const postsResponse = await fetch(\`/api/posts/\${user.id}\`);
    const posts = await postsResponse.json();

    return { user, posts };
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// 并行执行
async function fetchMultipleData() {
  const [users, posts, comments] = await Promise.all([
    fetch('/api/users').then(r => r.json()),
    fetch('/api/posts').then(r => r.json()),
    fetch('/api/comments').then(r => r.json())
  ]);

  return { users, posts, comments };
}`,
      useCases: ['简化异步代码', '错误处理', '并行执行', '条件异步操作'],
      relatedTerms: ['promise', 'try-catch', 'Promise.all', 'fetch']
    },
    {
      id: 'js_8',
      name: 'arrow_function',
      chinese: '箭头函数',
      description: 'ES6引入的函数简写语法，具有词法作用域的this绑定',
      difficulty: 'beginner',
      tags: ['es6', 'functions', 'syntax'],
      example: `// 传统函数
function add(a, b) {
  return a + b;
}

// 箭头函数
const add = (a, b) => a + b;

// 单参数可省略括号
const square = x => x * x;

// 多行函数体需要大括号和return
const processData = (data) => {
  const filtered = data.filter(item => item.active);
  const mapped = filtered.map(item => item.value);
  return mapped.reduce((sum, val) => sum + val, 0);
};

// this绑定差异
class Timer {
  constructor() {
    this.seconds = 0;
  }

  // 传统函数 - this会改变
  startTraditional() {
    setInterval(function() {
      this.seconds++; // this指向window/global
      console.log(this.seconds); // NaN
    }, 1000);
  }

  // 箭头函数 - this保持不变
  startArrow() {
    setInterval(() => {
      this.seconds++; // this指向Timer实例
      console.log(this.seconds); // 1, 2, 3...
    }, 1000);
  }
}`,
      useCases: ['简化函数语法', '回调函数', '数组方法', '保持this绑定'],
      relatedTerms: ['this', 'lexical scope', 'callback', 'es6']
    },
    {
      id: 'js_9',
      name: 'spread_operator',
      chinese: '展开运算符',
      description: '用三个点(...)表示的运算符，可以展开数组、对象或可迭代对象',
      difficulty: 'beginner',
      tags: ['es6', 'syntax', 'arrays', 'objects'],
      example: `// 数组展开
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2]; // [1, 2, 3, 4, 5, 6]

// 对象展开
const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const merged = { ...obj1, ...obj2 }; // { a: 1, b: 2, c: 3, d: 4 }

// 函数参数展开
function sum(a, b, c) {
  return a + b + c;
}
const numbers = [1, 2, 3];
console.log(sum(...numbers)); // 6

// 复制数组/对象
const originalArray = [1, 2, 3];
const copiedArray = [...originalArray];

const originalObj = { name: 'Alice', age: 25 };
const copiedObj = { ...originalObj };

// 字符串展开
const str = 'hello';
const chars = [...str]; // ['h', 'e', 'l', 'l', 'o']`,
      useCases: ['数组合并', '对象合并', '函数参数传递', '浅拷贝'],
      relatedTerms: ['rest parameters', 'destructuring', 'array methods']
    },
    {
      id: 'js_10',
      name: 'template_literals',
      chinese: '模板字符串',
      description: '使用反引号包围的字符串，支持变量插值和多行文本',
      difficulty: 'beginner',
      tags: ['es6', 'strings', 'syntax'],
      example: `// 基本用法
const name = 'Alice';
const age = 25;
const message = \`Hello, my name is \${name} and I'm \${age} years old.\`;

// 多行字符串
const html = \`
  <div class="user-card">
    <h2>\${name}</h2>
    <p>Age: \${age}</p>
    <p>Status: \${age >= 18 ? 'Adult' : 'Minor'}</p>
  </div>
\`;

// 表达式计算
const price = 100;
const tax = 0.08;
const total = \`Total: $\${(price * (1 + tax)).toFixed(2)}\`;

// 标签模板
function highlight(strings, ...values) {
  return strings.reduce((result, string, i) => {
    const value = values[i] ? \`<mark>\${values[i]}</mark>\` : '';
    return result + string + value;
  }, '');
}

const searchTerm = 'JavaScript';
const text = highlight\`Learn \${searchTerm} programming\`;
// "Learn <mark>JavaScript</mark> programming"`,
      useCases: ['字符串插值', 'HTML模板', '多行文本', '动态内容生成'],
      relatedTerms: ['string methods', 'interpolation', 'tagged templates']
    },
    {
      id: 'js_11',
      name: 'modules',
      chinese: 'ES6模块',
      description: 'JavaScript的模块化系统，支持导入导出功能，实现代码组织和复用',
      difficulty: 'intermediate',
      tags: ['es6', 'modules', 'import', 'export'],
      example: `// math.js - 导出模块
export const PI = 3.14159;

export function add(a, b) {
  return a + b;
}

export function multiply(a, b) {
  return a * b;
}

// 默认导出
export default function subtract(a, b) {
  return a - b;
}

// utils.js - 批量导出
const formatDate = (date) => date.toISOString();
const formatCurrency = (amount) => \`$\${amount.toFixed(2)}\`;

export { formatDate, formatCurrency };

// main.js - 导入模块
import subtract, { PI, add, multiply } from './math.js';
import { formatDate, formatCurrency } from './utils.js';

// 重命名导入
import { add as sum } from './math.js';

// 全部导入
import * as MathUtils from './math.js';

console.log(add(5, 3)); // 8
console.log(subtract(10, 4)); // 6
console.log(MathUtils.PI); // 3.14159

// 动态导入
async function loadModule() {
  const { add } = await import('./math.js');
  console.log(add(2, 3)); // 5
}`,
      useCases: ['代码组织', '功能复用', '依赖管理', '命名空间'],
      relatedTerms: ['import', 'export', 'bundling', 'tree shaking']
    },
    {
      id: 'js_12',
      name: 'classes',
      chinese: 'ES6类',
      description: 'ES6引入的类语法，提供更清晰的面向对象编程方式',
      difficulty: 'intermediate',
      tags: ['es6', 'oop', 'classes', 'inheritance'],
      example: `// 基本类定义
class Person {
  constructor(name, age) {
    this.name = name;
    this.age = age;
  }

  // 实例方法
  greet() {
    return \`Hello, I'm \${this.name}\`;
  }

  // 静态方法
  static createAdult(name) {
    return new Person(name, 18);
  }

  // Getter
  get info() {
    return \`\${this.name} (\${this.age})\`;
  }

  // Setter
  set age(value) {
    if (value < 0) throw new Error('Age cannot be negative');
    this._age = value;
  }

  get age() {
    return this._age;
  }
}

// 继承
class Student extends Person {
  constructor(name, age, school) {
    super(name, age); // 调用父类构造函数
    this.school = school;
  }

  greet() {
    return \`\${super.greet()}, I study at \${this.school}\`;
  }

  study() {
    return \`\${this.name} is studying\`;
  }
}

// 使用
const person = new Person('Alice', 25);
const student = new Student('Bob', 20, 'MIT');

console.log(person.greet()); // "Hello, I'm Alice"
console.log(student.greet()); // "Hello, I'm Bob, I study at MIT"
console.log(Person.createAdult('Charlie').age); // 18`,
      useCases: ['面向对象编程', '代码组织', '继承关系', '封装数据'],
      relatedTerms: ['constructor', 'inheritance', 'super', 'static methods']
    },
    {
      id: 'js_13',
      name: 'map_set',
      chinese: 'Map和Set',
      description: 'ES6新增的数据结构，Map用于键值对存储，Set用于唯一值集合',
      difficulty: 'intermediate',
      tags: ['es6', 'data-structures', 'collections'],
      example: `// Map - 键值对集合
const userMap = new Map();

// 设置值
userMap.set('name', 'Alice');
userMap.set('age', 25);
userMap.set(1, 'numeric key');

// 获取值
console.log(userMap.get('name')); // 'Alice'
console.log(userMap.has('age')); // true
console.log(userMap.size); // 3

// 遍历Map
for (const [key, value] of userMap) {
  console.log(\`\${key}: \${value}\`);
}

// Set - 唯一值集合
const uniqueNumbers = new Set([1, 2, 3, 3, 4, 4, 5]);
console.log(uniqueNumbers); // Set {1, 2, 3, 4, 5}

// 添加和删除
uniqueNumbers.add(6);
uniqueNumbers.delete(1);
console.log(uniqueNumbers.has(3)); // true

// 数组去重
const numbers = [1, 2, 2, 3, 3, 4];
const unique = [...new Set(numbers)]; // [1, 2, 3, 4]

// WeakMap 和 WeakSet
const weakMap = new WeakMap();
const obj = { name: 'test' };
weakMap.set(obj, 'some data');

const weakSet = new WeakSet();
weakSet.add(obj);`,
      useCases: ['数据去重', '键值对存储', '缓存实现', '对象关联'],
      relatedTerms: ['data structures', 'collections', 'iteration', 'weak references']
    },
    {
      id: 'js_14',
      name: 'generators',
      chinese: '生成器函数',
      description: '可以暂停和恢复执行的特殊函数，用于创建迭代器',
      difficulty: 'advanced',
      tags: ['es6', 'generators', 'iteration', 'async'],
      example: `// 基本生成器
function* numberGenerator() {
  yield 1;
  yield 2;
  yield 3;
  return 'done';
}

const gen = numberGenerator();
console.log(gen.next()); // { value: 1, done: false }
console.log(gen.next()); // { value: 2, done: false }
console.log(gen.next()); // { value: 3, done: false }
console.log(gen.next()); // { value: 'done', done: true }

// 无限序列生成器
function* fibonacci() {
  let a = 0, b = 1;
  while (true) {
    yield a;
    [a, b] = [b, a + b];
  }
}

const fib = fibonacci();
for (let i = 0; i < 10; i++) {
  console.log(fib.next().value);
}

// 生成器委托
function* gen1() {
  yield 1;
  yield 2;
}

function* gen2() {
  yield 3;
  yield 4;
}

function* combined() {
  yield* gen1();
  yield* gen2();
  yield 5;
}

console.log([...combined()]); // [1, 2, 3, 4, 5]

// 异步生成器
async function* asyncGenerator() {
  for (let i = 0; i < 3; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield i;
  }
}

// 使用异步生成器
(async () => {
  for await (const value of asyncGenerator()) {
    console.log(value); // 每秒输出 0, 1, 2
  }
})();`,
      useCases: ['迭代器创建', '惰性求值', '状态机', '异步数据流'],
      relatedTerms: ['iterators', 'yield', 'async generators', 'for...of']
    }
  ],

  // React生态系统 (60个术语)
  react: [
    {
      id: 'react_1',
      name: 'jsx',
      chinese: 'JavaScript XML',
      description: 'React中使用的语法扩展，允许在JavaScript中编写类似HTML的标记',
      difficulty: 'beginner',
      tags: ['syntax', 'components', 'rendering'],
      example: `const Welcome = ({ name, age }) => {
  const isAdult = age >= 18;

  return (
    <div className="welcome-container">
      <h1>Welcome, {name}!</h1>
      {isAdult ? (
        <p>You are an adult.</p>
      ) : (
        <p>You are a minor.</p>
      )}
      <ul>
        {['React', 'Vue', 'Angular'].map(framework => (
          <li key={framework}>{framework}</li>
        ))}
      </ul>
    </div>
  );
};`,
      useCases: ['组件定义', '模板渲染', '条件渲染', '列表渲染'],
      relatedTerms: ['components', 'props', 'virtual dom']
    },
    {
      id: 'react_2',
      name: 'hooks',
      chinese: 'React钩子',
      description: '让函数组件能够使用状态和其他React特性的特殊函数',
      difficulty: 'intermediate',
      tags: ['state', 'lifecycle', 'functional'],
      example: `import React, { useState, useEffect, useCallback } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchUser = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(\`/api/users/\${userId}\`);
      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error('Failed to fetch user:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
}`,
      useCases: ['状态管理', '副作用处理', '性能优化', '逻辑复用'],
      relatedTerms: ['useState', 'useEffect', 'useCallback', 'useMemo']
    },
    {
      id: 'react_3',
      name: 'virtual_dom',
      chinese: '虚拟DOM',
      description: 'React在内存中维护的DOM表示，用于优化实际DOM操作的性能',
      difficulty: 'intermediate',
      tags: ['performance', 'rendering', 'optimization'],
      example: `// React创建虚拟DOM元素
const element = React.createElement(
  'div',
  { className: 'container', id: 'main' },
  React.createElement('h1', null, 'Hello World'),
  React.createElement('p', null, 'This is a paragraph')
);

// JSX语法糖
const elementJSX = (
  <div className="container" id="main">
    <h1>Hello World</h1>
    <p>This is a paragraph</p>
  </div>
);

// 虚拟DOM对象结构
const virtualDOM = {
  type: 'div',
  props: {
    className: 'container',
    id: 'main',
    children: [
      {
        type: 'h1',
        props: { children: 'Hello World' }
      },
      {
        type: 'p',
        props: { children: 'This is a paragraph' }
      }
    ]
  }
};

// React的diff算法比较新旧虚拟DOM
// 只更新实际发生变化的DOM节点`,
      useCases: ['性能优化', '批量更新', '跨浏览器兼容', '状态管理'],
      relatedTerms: ['diff algorithm', 'reconciliation', 'rendering', 'jsx']
    },
    {
      id: 'react_4',
      name: 'state',
      chinese: '状态',
      description: 'React组件内部的数据，当状态改变时会触发组件重新渲染',
      difficulty: 'beginner',
      tags: ['data', 'rendering', 'component'],
      example: `// 类组件中的state
class Counter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      count: 0,
      message: 'Hello'
    };
  }

  increment = () => {
    this.setState(prevState => ({
      count: prevState.count + 1
    }));
  }

  render() {
    return (
      <div>
        <p>Count: {this.state.count}</p>
        <button onClick={this.increment}>+</button>
      </div>
    );
  }
}

// 函数组件中的state (使用useState Hook)
function CounterHook() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('Hello');

  const increment = () => {
    setCount(prevCount => prevCount + 1);
  };

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>+</button>
    </div>
  );
}`,
      useCases: ['数据管理', '用户交互', '动态内容', '表单处理'],
      relatedTerms: ['useState', 'setState', 'props', 'rendering']
    },
    {
      id: 'react_5',
      name: 'props',
      chinese: '属性',
      description: '父组件传递给子组件的数据，是只读的',
      difficulty: 'beginner',
      tags: ['data', 'component', 'communication'],
      example: `// 父组件传递props
function App() {
  const user = {
    name: 'Alice',
    age: 25,
    email: '<EMAIL>'
  };

  return (
    <div>
      <UserCard
        user={user}
        isActive={true}
        onEdit={() => console.log('Edit user')}
      />
    </div>
  );
}

// 子组件接收props
function UserCard({ user, isActive, onEdit }) {
  return (
    <div className={\`user-card \${isActive ? 'active' : ''}\`}>
      <h3>{user.name}</h3>
      <p>Age: {user.age}</p>
      <p>Email: {user.email}</p>
      <button onClick={onEdit}>Edit</button>
    </div>
  );
}

// 使用PropTypes进行类型检查
import PropTypes from 'prop-types';

UserCard.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string.isRequired,
    age: PropTypes.number.isRequired,
    email: PropTypes.string.isRequired
  }).isRequired,
  isActive: PropTypes.bool,
  onEdit: PropTypes.func.isRequired
};

UserCard.defaultProps = {
  isActive: false
};`,
      useCases: ['组件通信', '数据传递', '配置组件', '回调函数'],
      relatedTerms: ['state', 'component', 'PropTypes', 'children']
    },
    {
      id: 'react_6',
      name: 'useEffect',
      chinese: 'useEffect钩子',
      description: '处理副作用的React Hook，替代类组件的生命周期方法',
      difficulty: 'intermediate',
      tags: ['hooks', 'side-effects', 'lifecycle'],
      example: `import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // 组件挂载和userId变化时执行
  useEffect(() => {
    async function fetchUser() {
      setLoading(true);
      try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [userId]); // 依赖数组

  // 组件挂载时设置定时器，卸载时清理
  useEffect(() => {
    const timer = setInterval(() => {
      console.log('Timer tick');
    }, 1000);

    // 清理函数
    return () => {
      clearInterval(timer);
    };
  }, []); // 空依赖数组，只在挂载/卸载时执行

  // 每次渲染都执行（无依赖数组）
  useEffect(() => {
    document.title = user ? \`Profile: \${user.name}\` : 'Loading...';
  });

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}`,
      useCases: ['数据获取', '订阅管理', 'DOM操作', '定时器管理'],
      relatedTerms: ['useState', 'lifecycle', 'cleanup', 'dependencies']
    },
    {
      id: 'react_7',
      name: 'context',
      chinese: 'React Context',
      description: '跨组件层级传递数据的机制，避免props drilling问题',
      difficulty: 'intermediate',
      tags: ['context', 'state-management', 'props-drilling'],
      example: `import React, { createContext, useContext, useState } from 'react';

// 创建Context
const ThemeContext = createContext();
const UserContext = createContext();

// Provider组件
function App() {
  const [theme, setTheme] = useState('dark');
  const [user, setUser] = useState({ name: 'Alice', role: 'admin' });

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <UserContext.Provider value={{ user, setUser }}>
        <Header />
        <MainContent />
        <Footer />
      </UserContext.Provider>
    </ThemeContext.Provider>
  );
}

// 消费Context的组件
function Header() {
  const { theme, setTheme } = useContext(ThemeContext);
  const { user } = useContext(UserContext);

  return (
    <header className={\`header \${theme}\`}>
      <h1>Welcome, {user.name}</h1>
      <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
        Switch to {theme === 'dark' ? 'light' : 'dark'} theme
      </button>
    </header>
  );
}

// 自定义Hook封装Context
function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
}

function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within UserProvider');
  }
  return context;
}

// 使用自定义Hook
function ProfileCard() {
  const { theme } = useTheme();
  const { user } = useUser();

  return (
    <div className={\`profile-card \${theme}\`}>
      <h3>{user.name}</h3>
      <p>Role: {user.role}</p>
    </div>
  );
}`,
      useCases: ['全局状态', '主题切换', '用户认证', '多语言支持'],
      relatedTerms: ['provider', 'consumer', 'useContext', 'props drilling']
    },
    {
      id: 'react_8',
      name: 'memo',
      chinese: 'React.memo',
      description: '高阶组件，用于优化函数组件的渲染性能，类似PureComponent',
      difficulty: 'intermediate',
      tags: ['performance', 'optimization', 'memoization'],
      example: `import React, { memo, useState, useMemo, useCallback } from 'react';

// 普通组件 - 每次父组件更新都会重新渲染
function ExpensiveComponent({ data, onUpdate }) {
  console.log('ExpensiveComponent rendered');

  // 模拟昂贵的计算
  const processedData = data.map(item => ({
    ...item,
    processed: item.value * 2
  }));

  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => onUpdate(item.id)}>
          {item.name}: {item.processed}
        </div>
      ))}
    </div>
  );
}

// 使用memo优化的组件
const OptimizedComponent = memo(function OptimizedComponent({ data, onUpdate }) {
  console.log('OptimizedComponent rendered');

  const processedData = useMemo(() => {
    console.log('Processing data...');
    return data.map(item => ({
      ...item,
      processed: item.value * 2
    }));
  }, [data]);

  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => onUpdate(item.id)}>
          {item.name}: {item.processed}
        </div>
      ))}
    </div>
  );
});

// 自定义比较函数
const CustomMemoComponent = memo(function CustomMemoComponent({ user, settings }) {
  return (
    <div>
      <h3>{user.name}</h3>
      <p>Theme: {settings.theme}</p>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较逻辑
  return prevProps.user.id === nextProps.user.id &&
         prevProps.settings.theme === nextProps.settings.theme;
});

// 父组件
function ParentComponent() {
  const [count, setCount] = useState(0);
  const [data] = useState([
    { id: 1, name: 'Item 1', value: 10 },
    { id: 2, name: 'Item 2', value: 20 }
  ]);

  // 使用useCallback避免函数重新创建
  const handleUpdate = useCallback((id) => {
    console.log(\`Updated item \${id}\`);
  }, []);

  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>

      {/* 这个组件不会因为count变化而重新渲染 */}
      <OptimizedComponent data={data} onUpdate={handleUpdate} />
    </div>
  );
}`,
      useCases: ['性能优化', '避免不必要渲染', '昂贵计算缓存', '组件优化'],
      relatedTerms: ['useMemo', 'useCallback', 'PureComponent', 'performance']
    },
    {
      id: 'react_9',
      name: 'custom_hooks',
      chinese: '自定义Hook',
      description: '提取组件逻辑的自定义函数，实现逻辑复用和关注点分离',
      difficulty: 'intermediate',
      tags: ['hooks', 'reusability', 'custom'],
      example: `import { useState, useEffect, useCallback } from 'react';

// 自定义Hook：数据获取
function useFetch(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(\`HTTP error! status: \${response.status}\`);
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [url]);

  return { data, loading, error };
}

// 自定义Hook：本地存储
function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(\`Error reading localStorage key "\${key}":, error\`);
      return initialValue;
    }
  });

  const setValue = useCallback((value) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(\`Error setting localStorage key "\${key}":, error\`);
    }
  }, [key]);

  return [storedValue, setValue];
}

// 自定义Hook：计数器
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);

  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);

  return { count, increment, decrement, reset };
}

// 自定义Hook：防抖
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// 使用自定义Hook的组件
function UserProfile({ userId }) {
  const { data: user, loading, error } = useFetch(\`/api/users/\${userId}\`);
  const [preferences, setPreferences] = useLocalStorage('userPrefs', {});
  const { count, increment, decrement, reset } = useCounter(0);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>Profile views: {count}</p>
      <button onClick={increment}>View Profile</button>
      <button onClick={reset}>Reset Views</button>
    </div>
  );
}`,
      useCases: ['逻辑复用', '状态管理', '副作用封装', '组件解耦'],
      relatedTerms: ['hooks', 'reusability', 'separation of concerns', 'composition']
    }
  ],

  // Node.js后端 (70个术语)
  nodejs: [
    {
      id: 'node_1',
      name: 'middleware',
      chinese: '中间件',
      description: '在请求和响应之间执行的函数，用于处理请求、响应或传递控制权',
      difficulty: 'intermediate',
      tags: ['express', 'routing', 'processing'],
      example: `// 日志中间件
const loggerMiddleware = (req, res, next) => {
  console.log(\`\${new Date().toISOString()} - \${req.method} \${req.url}\`);
  next(); // 传递控制权给下一个中间件
};

// 认证中间件
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization;
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

// 使用中间件
app.use(loggerMiddleware);
app.use('/api/protected', authMiddleware);`,
      useCases: ['身份验证', '日志记录', '错误处理', '数据验证'],
      relatedTerms: ['express', 'routing', 'authentication']
    },
    {
      id: 'node_2',
      name: 'event_emitter',
      chinese: '事件发射器',
      description: 'Node.js中用于处理事件的核心模块，实现观察者模式',
      difficulty: 'intermediate',
      tags: ['events', 'async', 'patterns'],
      example: `const EventEmitter = require('events');

// 创建自定义事件发射器
class MyEmitter extends EventEmitter {}
const myEmitter = new MyEmitter();

// 监听事件
myEmitter.on('data', (data) => {
  console.log('Received data:', data);
});

myEmitter.on('error', (error) => {
  console.error('Error occurred:', error);
});

// 一次性监听器
myEmitter.once('start', () => {
  console.log('Started only once');
});

// 发射事件
myEmitter.emit('data', { message: 'Hello World' });
myEmitter.emit('start');
myEmitter.emit('start'); // 不会触发，因为是once

// 实际应用示例
class DatabaseConnection extends EventEmitter {
  connect() {
    setTimeout(() => {
      this.emit('connected', { host: 'localhost', port: 5432 });
    }, 1000);
  }

  query(sql) {
    if (Math.random() > 0.8) {
      this.emit('error', new Error('Query failed'));
    } else {
      this.emit('result', { rows: [], count: 0 });
    }
  }
}`,
      useCases: ['异步通信', '解耦组件', '状态通知', '流处理'],
      relatedTerms: ['observer pattern', 'async', 'streams', 'callbacks']
    },
    {
      id: 'node_3',
      name: 'streams',
      chinese: '流',
      description: '用于处理大量数据的抽象接口，支持读取、写入、转换数据',
      difficulty: 'advanced',
      tags: ['data', 'performance', 'memory'],
      example: `const fs = require('fs');
const { Transform } = require('stream');

// 读取流
const readStream = fs.createReadStream('large-file.txt', {
  encoding: 'utf8',
  highWaterMark: 1024 // 缓冲区大小
});

// 写入流
const writeStream = fs.createWriteStream('output.txt');

// 转换流 - 将文本转为大写
const upperCaseTransform = new Transform({
  transform(chunk, encoding, callback) {
    this.push(chunk.toString().toUpperCase());
    callback();
  }
});

// 管道操作
readStream
  .pipe(upperCaseTransform)
  .pipe(writeStream);

// 处理事件
readStream.on('data', (chunk) => {
  console.log(\`Received \${chunk.length} bytes\`);
});

readStream.on('end', () => {
  console.log('File reading completed');
});

readStream.on('error', (error) => {
  console.error('Read error:', error);
});

// HTTP请求流处理
const http = require('http');

const server = http.createServer((req, res) => {
  if (req.method === 'POST') {
    let body = '';

    req.on('data', (chunk) => {
      body += chunk.toString();
    });

    req.on('end', () => {
      console.log('Request body:', body);
      res.end('Data received');
    });
  }
});`,
      useCases: ['文件处理', '网络传输', '数据转换', '内存优化'],
      relatedTerms: ['buffer', 'pipe', 'events', 'memory management']
    },
    {
      id: 'node_4',
      name: 'buffer',
      chinese: '缓冲区',
      description: '用于处理二进制数据的全局对象，在文件和网络操作中广泛使用',
      difficulty: 'intermediate',
      tags: ['binary', 'data', 'memory'],
      example: `// 创建Buffer
const buf1 = Buffer.alloc(10); // 分配10字节的零填充缓冲区
const buf2 = Buffer.from('Hello World', 'utf8'); // 从字符串创建
const buf3 = Buffer.from([0x48, 0x65, 0x6c, 0x6c, 0x6f]); // 从数组创建

// Buffer操作
console.log(buf2.toString()); // "Hello World"
console.log(buf2.toString('hex')); // "48656c6c6f20576f726c64"
console.log(buf2.toString('base64')); // "SGVsbG8gV29ybGQ="

// 写入和读取
const buffer = Buffer.alloc(20);
buffer.write('Node.js', 0, 'utf8');
console.log(buffer.toString('utf8', 0, 7)); // "Node.js"

// 拷贝和切片
const source = Buffer.from('Hello World');
const target = Buffer.alloc(5);
source.copy(target, 0, 0, 5);
console.log(target.toString()); // "Hello"

const slice = source.slice(6, 11);
console.log(slice.toString()); // "World"

// 文件操作中的Buffer
const fs = require('fs');

fs.readFile('image.jpg', (err, data) => {
  if (err) throw err;
  console.log(\`File size: \${data.length} bytes\`);

  // 写入文件
  fs.writeFile('copy.jpg', data, (err) => {
    if (err) throw err;
    console.log('File copied successfully');
  });
});`,
      useCases: ['文件操作', '网络传输', '加密解密', '图像处理'],
      relatedTerms: ['streams', 'encoding', 'binary data', 'file system']
    },
    {
      id: 'node_5',
      name: 'express_routing',
      chinese: 'Express路由',
      description: 'Express框架中定义应用程序端点的机制，处理不同HTTP请求',
      difficulty: 'beginner',
      tags: ['express', 'routing', 'http'],
      example: `const express = require('express');
const app = express();

// 基本路由
app.get('/', (req, res) => {
  res.send('Hello World!');
});

app.post('/users', (req, res) => {
  res.json({ message: 'User created' });
});

// 路由参数
app.get('/users/:id', (req, res) => {
  const userId = req.params.id;
  res.json({ userId, message: \`User \${userId} details\` });
});

// 查询参数
app.get('/search', (req, res) => {
  const { q, limit = 10 } = req.query;
  res.json({ query: q, limit: parseInt(limit) });
});

// 路由模式匹配
app.get('/files/*', (req, res) => {
  const filePath = req.params[0];
  res.send(\`File path: \${filePath}\`);
});

// 多个处理函数
app.get('/protected',
  (req, res, next) => {
    // 中间件：验证token
    const token = req.headers.authorization;
    if (!token) {
      return res.status(401).json({ error: 'No token' });
    }
    next();
  },
  (req, res) => {
    res.json({ message: 'Protected resource' });
  }
);

// 路由器
const userRouter = express.Router();

userRouter.get('/', (req, res) => {
  res.json({ users: [] });
});

userRouter.post('/', (req, res) => {
  res.json({ message: 'User created' });
});

userRouter.put('/:id', (req, res) => {
  res.json({ message: \`User \${req.params.id} updated\` });
});

// 挂载路由器
app.use('/api/users', userRouter);

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});`,
      useCases: ['API设计', 'RESTful服务', '路径匹配', '请求处理'],
      relatedTerms: ['middleware', 'http methods', 'parameters', 'express']
    },
    {
      id: 'node_6',
      name: 'npm_packages',
      chinese: 'NPM包管理',
      description: 'Node.js的包管理器，用于安装、管理和发布JavaScript包',
      difficulty: 'beginner',
      tags: ['npm', 'packages', 'dependencies'],
      example: `// package.json 配置
{
  "name": "my-project",
  "version": "1.0.0",
  "description": "A sample Node.js project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest",
    "build": "webpack --mode production",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^6.0.0",
    "jsonwebtoken": "^8.5.1"
  },
  "devDependencies": {
    "nodemon": "^2.0.0",
    "jest": "^28.0.0",
    "eslint": "^8.0.0",
    "prettier": "^2.0.0"
  },
  "engines": {
    "node": ">=14.0.0",
    "npm": ">=6.0.0"
  }
}

// 常用NPM命令
/*
# 初始化项目
npm init
npm init -y

# 安装依赖
npm install express
npm install --save express
npm install --save-dev nodemon
npm install -g typescript

# 更新依赖
npm update
npm update express
npm outdated

# 卸载依赖
npm uninstall express
npm uninstall --save-dev nodemon

# 运行脚本
npm start
npm run dev
npm test

# 发布包
npm login
npm publish
npm version patch
*/

// 使用已安装的包
const express = require('express');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');

// 检查包版本
console.log('Express version:', require('express/package.json').version);

// 环境变量配置
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// 条件加载开发依赖
if (NODE_ENV === 'development') {
  const nodemon = require('nodemon');
}`,
      useCases: ['依赖管理', '项目配置', '脚本自动化', '包发布'],
      relatedTerms: ['package.json', 'node_modules', 'semantic versioning', 'scripts']
    },
    {
      id: 'node_7',
      name: 'async_patterns',
      chinese: '异步编程模式',
      description: 'Node.js中处理异步操作的各种模式和最佳实践',
      difficulty: 'intermediate',
      tags: ['async', 'patterns', 'callbacks', 'promises'],
      example: `// 1. 回调模式 (Callback Pattern)
const fs = require('fs');

function readFileCallback(filename, callback) {
  fs.readFile(filename, 'utf8', (err, data) => {
    if (err) {
      return callback(err, null);
    }
    callback(null, data);
  });
}

// 使用回调
readFileCallback('file.txt', (err, data) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('File content:', data);
  }
});

// 2. Promise模式
const { promisify } = require('util');
const readFilePromise = promisify(fs.readFile);

function readFileWithPromise(filename) {
  return readFilePromise(filename, 'utf8');
}

// 使用Promise
readFileWithPromise('file.txt')
  .then(data => console.log('File content:', data))
  .catch(err => console.error('Error:', err));

// 3. Async/Await模式
async function readFileAsync(filename) {
  try {
    const data = await readFilePromise(filename, 'utf8');
    return data;
  } catch (error) {
    throw new Error(\`Failed to read file: \${error.message}\`);
  }
}

// 使用async/await
async function processFile() {
  try {
    const content = await readFileAsync('file.txt');
    console.log('File content:', content);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// 4. 并行处理
async function readMultipleFiles() {
  try {
    const [file1, file2, file3] = await Promise.all([
      readFileAsync('file1.txt'),
      readFileAsync('file2.txt'),
      readFileAsync('file3.txt')
    ]);

    return { file1, file2, file3 };
  } catch (error) {
    console.error('Error reading files:', error);
  }
}

// 5. 错误处理模式
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 6. 流式处理
const { pipeline } = require('stream');
const { createReadStream, createWriteStream } = require('fs');
const { createGzip } = require('zlib');

pipeline(
  createReadStream('input.txt'),
  createGzip(),
  createWriteStream('output.txt.gz'),
  (error) => {
    if (error) {
      console.error('Pipeline failed:', error);
    } else {
      console.log('Pipeline succeeded');
    }
  }
);`,
      useCases: ['文件操作', '网络请求', '数据库查询', '错误处理'],
      relatedTerms: ['callbacks', 'promises', 'async/await', 'error handling']
    },
    {
      id: 'node_8',
      name: 'environment_variables',
      chinese: '环境变量',
      description: '用于配置应用程序的外部变量，实现配置与代码分离',
      difficulty: 'beginner',
      tags: ['configuration', 'environment', 'security'],
      example: `// 1. 读取环境变量
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';
const DATABASE_URL = process.env.DATABASE_URL;
const JWT_SECRET = process.env.JWT_SECRET;

console.log('Server running on port:', PORT);
console.log('Environment:', NODE_ENV);

// 2. 使用dotenv包
// npm install dotenv
require('dotenv').config();

// .env 文件内容
/*
PORT=3000
NODE_ENV=development
DATABASE_URL=mongodb://localhost:27017/myapp
JWT_SECRET=your-secret-key
API_KEY=your-api-key
REDIS_URL=redis://localhost:6379
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-password
*/

// 3. 配置管理
const config = {
  development: {
    port: process.env.PORT || 3000,
    database: {
      url: process.env.DATABASE_URL || 'mongodb://localhost:27017/myapp-dev',
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true
      }
    },
    jwt: {
      secret: process.env.JWT_SECRET || 'dev-secret',
      expiresIn: '1h'
    },
    logging: {
      level: 'debug'
    }
  },
  production: {
    port: process.env.PORT || 80,
    database: {
      url: process.env.DATABASE_URL,
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        ssl: true
      }
    },
    jwt: {
      secret: process.env.JWT_SECRET,
      expiresIn: '15m'
    },
    logging: {
      level: 'error'
    }
  }
};

const currentConfig = config[NODE_ENV] || config.development;

// 4. 验证必需的环境变量
function validateEnvironment() {
  const required = ['DATABASE_URL', 'JWT_SECRET'];
  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    process.exit(1);
  }
}

if (NODE_ENV === 'production') {
  validateEnvironment();
}

// 5. 类型转换和默认值
const config = {
  port: parseInt(process.env.PORT) || 3000,
  enableLogging: process.env.ENABLE_LOGGING === 'true',
  maxConnections: parseInt(process.env.MAX_CONNECTIONS) || 100,
  timeout: parseFloat(process.env.TIMEOUT) || 30.0,
  allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000']
};

module.exports = currentConfig;`,
      useCases: ['应用配置', '敏感信息保护', '环境区分', '部署配置'],
      relatedTerms: ['dotenv', 'configuration', 'security', 'deployment']
    }
  ],

  // 数据库相关 (80个术语)
  database: [
    {
      id: 'db_1',
      name: 'nosql',
      chinese: '非关系型数据库',
      description: '不使用传统表格关系模型的数据库，提供更灵活的数据存储方式',
      difficulty: 'intermediate',
      tags: ['database', 'scalability', 'flexibility'],
      example: `// MongoDB 文档示例
{
  "_id": ObjectId("507f1f77bcf86cd799439011"),
  "name": "Alice Johnson",
  "email": "<EMAIL>",
  "profile": {
    "age": 28,
    "location": "San Francisco",
    "skills": ["JavaScript", "Python", "React"],
    "experience": [
      {
        "company": "TechCorp",
        "position": "Frontend Developer",
        "duration": "2020-2023"
      }
    ]
  },
  "preferences": {
    "theme": "dark",
    "notifications": true
  },
  "createdAt": ISODate("2023-01-15T08:00:00Z")
}`,
      useCases: ['大数据处理', '实时应用', '内容管理', '物联网'],
      relatedTerms: ['mongodb', 'document store', 'key-value']
    },
    {
      id: 'db_2',
      name: 'sql',
      chinese: '结构化查询语言',
      description: '用于管理关系型数据库的标准语言，支持数据查询、插入、更新和删除',
      difficulty: 'beginner',
      tags: ['database', 'query', 'relational'],
      example: `-- 创建表
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  age INTEGER CHECK (age >= 0),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO users (name, email, age)
VALUES
  ('Alice Johnson', '<EMAIL>', 28),
  ('Bob Smith', '<EMAIL>', 32),
  ('Carol Davis', '<EMAIL>', 25);

-- 查询数据
SELECT name, email, age
FROM users
WHERE age > 25
ORDER BY age DESC
LIMIT 10;

-- 更新数据
UPDATE users
SET age = 29
WHERE email = '<EMAIL>';

-- 删除数据
DELETE FROM users
WHERE age < 18;

-- 连接查询
SELECT u.name, p.title, p.content
FROM users u
INNER JOIN posts p ON u.id = p.user_id
WHERE u.age > 25;

-- 聚合查询
SELECT
  COUNT(*) as total_users,
  AVG(age) as average_age,
  MIN(age) as youngest,
  MAX(age) as oldest
FROM users;`,
      useCases: ['数据查询', '数据分析', '报表生成', '数据管理'],
      relatedTerms: ['database', 'relational', 'joins', 'indexes']
    },
    {
      id: 'db_3',
      name: 'orm',
      chinese: '对象关系映射',
      description: '将数据库表映射为编程语言对象的技术，简化数据库操作',
      difficulty: 'intermediate',
      tags: ['database', 'abstraction', 'mapping'],
      example: `// Sequelize ORM 示例 (Node.js)
const { Sequelize, DataTypes } = require('sequelize');

// 连接数据库
const sequelize = new Sequelize('database', 'username', 'password', {
  host: 'localhost',
  dialect: 'postgres'
});

// 定义模型
const User = sequelize.define('User', {
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  age: {
    type: DataTypes.INTEGER,
    validate: {
      min: 0,
      max: 120
    }
  }
});

const Post = sequelize.define('Post', {
  title: DataTypes.STRING,
  content: DataTypes.TEXT
});

// 定义关联
User.hasMany(Post);
Post.belongsTo(User);

// 使用模型
async function createUser() {
  const user = await User.create({
    name: 'Alice Johnson',
    email: '<EMAIL>',
    age: 28
  });

  const post = await Post.create({
    title: 'My First Post',
    content: 'Hello World!',
    UserId: user.id
  });

  return user;
}

// 查询数据
async function getUsers() {
  const users = await User.findAll({
    where: {
      age: {
        [Sequelize.Op.gt]: 25
      }
    },
    include: [Post],
    order: [['age', 'DESC']]
  });

  return users;
}`,
      useCases: ['简化数据库操作', '类型安全', '关系管理', '数据验证'],
      relatedTerms: ['models', 'migrations', 'associations', 'validation']
    },
    {
      id: 'db_4',
      name: 'acid',
      chinese: 'ACID特性',
      description: '数据库事务必须满足的四个特性：原子性、一致性、隔离性、持久性',
      difficulty: 'advanced',
      tags: ['database', 'transactions', 'reliability'],
      example: `-- 原子性 (Atomicity) 示例
BEGIN TRANSACTION;

UPDATE accounts SET balance = balance - 100 WHERE id = 1;
UPDATE accounts SET balance = balance + 100 WHERE id = 2;

-- 如果任何一个操作失败，整个事务回滚
COMMIT; -- 或 ROLLBACK;

-- 一致性 (Consistency) 示例
-- 约束确保数据一致性
ALTER TABLE accounts
ADD CONSTRAINT check_balance
CHECK (balance >= 0);

-- 隔离性 (Isolation) 示例
-- 不同隔离级别
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
-- READ UNCOMMITTED, READ COMMITTED, REPEATABLE READ, SERIALIZABLE

-- 持久性 (Durability) 示例
-- 事务提交后，数据永久保存
BEGIN TRANSACTION;
INSERT INTO orders (user_id, product_id, quantity)
VALUES (1, 100, 2);
COMMIT; -- 数据持久化到磁盘

-- Node.js 中的事务处理
const { Pool } = require('pg');
const pool = new Pool();

async function transferMoney(fromId, toId, amount) {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // 检查余额
    const fromAccount = await client.query(
      'SELECT balance FROM accounts WHERE id = $1',
      [fromId]
    );

    if (fromAccount.rows[0].balance < amount) {
      throw new Error('Insufficient funds');
    }

    // 执行转账
    await client.query(
      'UPDATE accounts SET balance = balance - $1 WHERE id = $2',
      [amount, fromId]
    );

    await client.query(
      'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
      [amount, toId]
    );

    await client.query('COMMIT');
    console.log('Transfer completed successfully');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Transfer failed:', error.message);
    throw error;
  } finally {
    client.release();
  }
}`,
      useCases: ['金融系统', '电商平台', '库存管理', '数据完整性'],
      relatedTerms: ['transactions', 'consistency', 'isolation levels', 'rollback']
    },
    {
      id: 'db_5',
      name: 'indexing',
      chinese: '数据库索引',
      description: '提高数据库查询性能的数据结构，通过创建指向数据的指针来加速查找',
      difficulty: 'intermediate',
      tags: ['performance', 'optimization', 'indexing'],
      example: `-- 创建索引
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_name ON users(name);
CREATE INDEX idx_post_created_at ON posts(created_at);

-- 复合索引
CREATE INDEX idx_user_age_city ON users(age, city);
CREATE INDEX idx_post_user_date ON posts(user_id, created_at);

-- 唯一索引
CREATE UNIQUE INDEX idx_user_username ON users(username);

-- 部分索引
CREATE INDEX idx_active_users ON users(email) WHERE active = true;

-- 函数索引
CREATE INDEX idx_user_lower_email ON users(LOWER(email));

-- 查看索引使用情况
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';

-- MongoDB索引示例
// 创建单字段索引
db.users.createIndex({ email: 1 });

// 创建复合索引
db.posts.createIndex({ userId: 1, createdAt: -1 });

// 创建文本索引
db.articles.createIndex({ title: "text", content: "text" });

// 创建地理空间索引
db.locations.createIndex({ coordinates: "2dsphere" });

// 查看索引
db.users.getIndexes();

// 查询性能分析
db.users.find({ email: "<EMAIL>" }).explain("executionStats");

// Node.js中使用索引
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    index: true
  },
  age: Number,
  city: String,
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
});

// 复合索引
userSchema.index({ age: 1, city: 1 });

// 文本索引
userSchema.index({ name: 'text', bio: 'text' });

const User = mongoose.model('User', userSchema);`,
      useCases: ['查询优化', '性能提升', '数据检索', '排序加速'],
      relatedTerms: ['performance', 'query optimization', 'btree', 'hash index']
    },
    {
      id: 'db_6',
      name: 'mongodb_aggregation',
      chinese: 'MongoDB聚合',
      description: 'MongoDB的数据处理管道，用于复杂的数据分析和转换操作',
      difficulty: 'advanced',
      tags: ['mongodb', 'aggregation', 'data-processing'],
      example: `// 基本聚合管道
db.orders.aggregate([
  // 阶段1: 匹配条件
  { $match: { status: "completed", date: { $gte: new Date("2023-01-01") } } },

  // 阶段2: 分组和计算
  { $group: {
    _id: "$customerId",
    totalAmount: { $sum: "$amount" },
    orderCount: { $sum: 1 },
    avgAmount: { $avg: "$amount" },
    maxAmount: { $max: "$amount" }
  }},

  // 阶段3: 排序
  { $sort: { totalAmount: -1 } },

  // 阶段4: 限制结果
  { $limit: 10 }
]);

// 复杂聚合示例
db.products.aggregate([
  // 展开数组字段
  { $unwind: "$categories" },

  // 查找关联数据
  { $lookup: {
    from: "categories",
    localField: "categories",
    foreignField: "_id",
    as: "categoryInfo"
  }},

  // 展开查找结果
  { $unwind: "$categoryInfo" },

  // 分组统计
  { $group: {
    _id: "$categoryInfo.name",
    productCount: { $sum: 1 },
    avgPrice: { $avg: "$price" },
    products: { $push: {
      name: "$name",
      price: "$price"
    }}
  }},

  // 添加计算字段
  { $addFields: {
    priceRange: {
      $switch: {
        branches: [
          { case: { $lt: ["$avgPrice", 50] }, then: "Budget" },
          { case: { $lt: ["$avgPrice", 200] }, then: "Mid-range" },
          { case: { $gte: ["$avgPrice", 200] }, then: "Premium" }
        ],
        default: "Unknown"
      }
    }
  }},

  // 投影输出字段
  { $project: {
    categoryName: "$_id",
    productCount: 1,
    avgPrice: { $round: ["$avgPrice", 2] },
    priceRange: 1,
    topProducts: { $slice: ["$products", 3] }
  }}
]);

// 时间序列聚合
db.sales.aggregate([
  { $match: {
    date: {
      $gte: new Date("2023-01-01"),
      $lt: new Date("2024-01-01")
    }
  }},

  // 按月分组
  { $group: {
    _id: {
      year: { $year: "$date" },
      month: { $month: "$date" }
    },
    totalSales: { $sum: "$amount" },
    orderCount: { $sum: 1 },
    uniqueCustomers: { $addToSet: "$customerId" }
  }},

  // 计算唯一客户数
  { $addFields: {
    uniqueCustomerCount: { $size: "$uniqueCustomers" }
  }},

  // 排序
  { $sort: { "_id.year": 1, "_id.month": 1 } }
]);

// Node.js中使用聚合
const mongoose = require('mongoose');

async function getTopCustomers() {
  const result = await Order.aggregate([
    { $match: { status: 'completed' } },
    { $group: {
      _id: '$customerId',
      totalSpent: { $sum: '$amount' },
      orderCount: { $sum: 1 }
    }},
    { $lookup: {
      from: 'users',
      localField: '_id',
      foreignField: '_id',
      as: 'customer'
    }},
    { $unwind: '$customer' },
    { $project: {
      customerName: '$customer.name',
      customerEmail: '$customer.email',
      totalSpent: 1,
      orderCount: 1
    }},
    { $sort: { totalSpent: -1 } },
    { $limit: 10 }
  ]);

  return result;
}`,
      useCases: ['数据分析', '报表生成', '统计计算', '数据转换'],
      relatedTerms: ['pipeline', 'group', 'lookup', 'match']
    },
    {
      id: 'db_7',
      name: 'database_migrations',
      chinese: '数据库迁移',
      description: '管理数据库结构变更的版本控制系统，确保数据库schema的一致性',
      difficulty: 'intermediate',
      tags: ['migrations', 'schema', 'version-control'],
      example: `// Sequelize迁移示例
// migrations/20231201000001-create-users.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Users', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加索引
    await queryInterface.addIndex('Users', ['email']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Users');
  }
};

// migrations/20231202000001-add-user-profile.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Users', 'bio', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    await queryInterface.addColumn('Users', 'avatar', {
      type: Sequelize.STRING,
      allowNull: true
    });

    await queryInterface.addColumn('Users', 'isActive', {
      type: Sequelize.BOOLEAN,
      defaultValue: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Users', 'bio');
    await queryInterface.removeColumn('Users', 'avatar');
    await queryInterface.removeColumn('Users', 'isActive');
  }
};

// Knex.js迁移示例
// migrations/20231201000001_create_users_table.js
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.increments('id').primary();
    table.string('email').notNullable().unique();
    table.string('name').notNullable();
    table.text('bio');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);

    // 添加索引
    table.index(['email']);
    table.index(['is_active']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('users');
};

// 运行迁移的命令
/*
# Sequelize
npx sequelize-cli db:migrate
npx sequelize-cli db:migrate:undo
npx sequelize-cli db:migrate:undo:all

# Knex
npx knex migrate:latest
npx knex migrate:rollback
npx knex migrate:rollback --all

# Prisma
npx prisma migrate dev
npx prisma migrate deploy
npx prisma migrate reset
*/

// Prisma迁移示例
// schema.prisma
/*
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  bio       String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  posts     Post[]

  @@index([email])
  @@index([isActive])
}

model Post {
  id        Int      @id @default(autoincrement())
  title     String
  content   String?
  published Boolean  @default(false)
  authorId  Int
  author    User     @relation(fields: [authorId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([authorId])
  @@index([published])
}
*/

// 种子数据 (seeders)
// seeders/20231201000001-demo-users.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('Users', [
      {
        email: '<EMAIL>',
        name: 'Alice Johnson',
        bio: 'Software developer',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        name: 'Bob Smith',
        bio: 'Product manager',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('Users', null, {});
  }
};`,
      useCases: ['版本控制', '团队协作', '部署管理', '数据库同步'],
      relatedTerms: ['schema', 'version control', 'rollback', 'seeders']
    }
  ],

  // CSS与前端样式 (50个术语)
  css: [
    {
      id: 'css_1',
      name: 'css_grid',
      chinese: 'CSS网格布局',
      description: '二维布局系统，用于创建复杂的网页布局',
      difficulty: 'intermediate',
      tags: ['layout', 'responsive', 'design'],
      example: `.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header header header"
    "sidebar main main"
    "footer footer footer";
  gap: 20px;
  min-height: 100vh;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.footer { grid-area: footer; }

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    grid-template-areas:
      "header"
      "main"
      "sidebar"
      "footer";
    grid-template-columns: 1fr;
  }
}`,
      useCases: ['页面布局', '响应式设计', '组件排列', '复杂界面'],
      relatedTerms: ['flexbox', 'responsive design', 'media queries']
    },
    {
      id: 'css_2',
      name: 'flexbox',
      chinese: 'Flexbox弹性布局',
      description: '一维布局方法，用于在容器中分配空间和对齐项目',
      difficulty: 'beginner',
      tags: ['layout', 'alignment', 'responsive'],
      example: `/* 基本Flexbox容器 */
.flex-container {
  display: flex;
  justify-content: space-between; /* 主轴对齐 */
  align-items: center; /* 交叉轴对齐 */
  flex-wrap: wrap; /* 允许换行 */
  gap: 1rem; /* 项目间距 */
}

/* Flex项目属性 */
.flex-item {
  flex: 1; /* flex-grow: 1, flex-shrink: 1, flex-basis: 0% */
}

.flex-item-special {
  flex: 2 1 200px; /* grow: 2, shrink: 1, basis: 200px */
  align-self: flex-start; /* 单独对齐 */
}

/* 常用布局模式 */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.card {
  flex: 1 1 300px; /* 最小宽度300px，可伸缩 */
}

/* 垂直居中 */
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}`,
      useCases: ['导航栏', '卡片布局', '居中对齐', '响应式组件'],
      relatedTerms: ['css grid', 'alignment', 'responsive', 'layout']
    },
    {
      id: 'css_3',
      name: 'css_variables',
      chinese: 'CSS自定义属性',
      description: '可重用的CSS值，支持动态更新和主题切换',
      difficulty: 'intermediate',
      tags: ['variables', 'theming', 'maintainability'],
      example: `:root {
  /* 颜色系统 */
  --primary-color: #3b82f6;
  --secondary-color: #8b5cf6;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;

  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* 字体系统 */
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 使用CSS变量 */
.button {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-md);
  border: none;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.button:hover {
  background-color: var(--primary-color-dark, #2563eb);
  box-shadow: var(--shadow-lg);
}

/* 主题切换 */
[data-theme="dark"] {
  --primary-color: #60a5fa;
  --background-color: #1f2937;
  --text-color: #f9fafb;
}

/* JavaScript动态更新 */
/*
document.documentElement.style.setProperty('--primary-color', '#ff6b6b');
*/`,
      useCases: ['主题系统', '设计系统', '动态样式', '代码维护'],
      relatedTerms: ['theming', 'design system', 'maintainability', 'dynamic styles']
    },
    {
      id: 'css_4',
      name: 'responsive_design',
      chinese: '响应式设计',
      description: '使网页在不同设备和屏幕尺寸上都能良好显示的设计方法',
      difficulty: 'intermediate',
      tags: ['responsive', 'media-queries', 'mobile'],
      example: `/* 移动优先的响应式设计 */
.container {
  width: 100%;
  padding: 1rem;
  margin: 0 auto;
}

/* 平板设备 */
@media (min-width: 768px) {
  .container {
    max-width: 750px;
    padding: 2rem;
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* 桌面设备 */
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
  }

  .grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .sidebar {
    display: block;
  }
}

/* 大屏设备 */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
  }

  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 响应式字体 */
.title {
  font-size: clamp(1.5rem, 4vw, 3rem);
  line-height: 1.2;
}

.text {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
}

/* 响应式图片 */
.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* 现代响应式单位 */
.card {
  width: min(100%, 400px);
  padding: max(1rem, 3vw);
  margin: clamp(1rem, 5vw, 3rem) auto;
}

/* 容器查询 (Container Queries) */
.card-container {
  container-type: inline-size;
}

@container (min-width: 300px) {
  .card {
    display: flex;
    flex-direction: row;
  }
}

@container (min-width: 500px) {
  .card {
    padding: 2rem;
  }

  .card-image {
    width: 40%;
  }

  .card-content {
    width: 60%;
  }
}

/* 响应式导航 */
.nav {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .nav {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .nav-menu {
    display: flex;
    gap: 2rem;
  }

  .nav-toggle {
    display: none;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none;
  }

  .container {
    max-width: none;
    padding: 0;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}`,
      useCases: ['多设备适配', '移动端优化', '用户体验', '可访问性'],
      relatedTerms: ['media queries', 'viewport', 'mobile-first', 'container queries']
    },
    {
      id: 'css_5',
      name: 'css_animations',
      chinese: 'CSS动画',
      description: '使用CSS创建平滑的动画效果，包括过渡和关键帧动画',
      difficulty: 'intermediate',
      tags: ['animations', 'transitions', 'keyframes'],
      example: `/* CSS过渡 (Transitions) */
.button {
  background-color: #3b82f6;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* 关键帧动画 (Keyframe Animations) */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 应用动画 */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.bounce-element {
  animation: bounce 2s infinite;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* 复杂动画序列 */
@keyframes slideInScale {
  0% {
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateX(0) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.card {
  animation: slideInScale 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停动画 */
.image-container {
  overflow: hidden;
  border-radius: 0.5rem;
}

.image-container img {
  transition: transform 0.3s ease;
}

.image-container:hover img {
  transform: scale(1.1);
}

/* 文字动画 */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  50% {
    border-color: transparent;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation:
    typewriter 3s steps(40, end),
    blink 0.75s step-end infinite;
}

/* 滚动触发动画 */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* 性能优化 */
.optimized-animation {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 动画控制 */
.paused {
  animation-play-state: paused;
}

.delayed {
  animation-delay: 0.5s;
}

.slow {
  animation-duration: 2s;
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}`,
      useCases: ['用户交互', '视觉反馈', '页面转场', '加载状态'],
      relatedTerms: ['transitions', 'keyframes', 'transform', 'performance']
    },
    {
      id: 'css_6',
      name: 'css_preprocessors',
      chinese: 'CSS预处理器',
      description: '扩展CSS功能的工具，如Sass、Less，提供变量、嵌套、混合等特性',
      difficulty: 'intermediate',
      tags: ['sass', 'less', 'preprocessing', 'variables'],
      example: `// Sass/SCSS 示例
// _variables.scss
$primary-color: #3b82f6;
$secondary-color: #8b5cf6;
$font-size-base: 1rem;
$font-size-lg: 1.25rem;
$border-radius: 0.5rem;
$spacing-unit: 1rem;

// 颜色函数
$primary-light: lighten($primary-color, 20%);
$primary-dark: darken($primary-color, 20%);

// _mixins.scss
@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  padding: $spacing-unit * 0.75 $spacing-unit * 1.5;
  border: none;
  border-radius: $border-radius;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: darken($bg-color, 10%);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

@mixin responsive($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 主样式文件
.button {
  @include button-style($primary-color);

  &--secondary {
    @include button-style($secondary-color);
  }

  &--large {
    font-size: $font-size-lg;
    padding: $spacing-unit $spacing-unit * 2;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-unit;

  @include responsive(mobile) {
    padding: $spacing-unit * 0.5;
  }

  @include responsive(desktop) {
    padding: $spacing-unit * 2;
  }
}

// 嵌套规则
.navigation {
  background-color: $primary-color;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    @include flex-center;

    li {
      margin: 0 $spacing-unit;

      a {
        color: white;
        text-decoration: none;
        padding: $spacing-unit * 0.5;
        border-radius: $border-radius;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: rgba(white, 0.1);
        }

        &.active {
          background-color: rgba(white, 0.2);
          font-weight: bold;
        }
      }
    }
  }
}

// 循环和条件
@for $i from 1 through 12 {
  .col-#{$i} {
    width: percentage($i / 12);
  }
}

$colors: (
  primary: $primary-color,
  secondary: $secondary-color,
  success: #10b981,
  warning: #f59e0b,
  danger: #ef4444
);

@each $name, $color in $colors {
  .text-#{$name} {
    color: $color;
  }

  .bg-#{$name} {
    background-color: $color;
  }

  .border-#{$name} {
    border-color: $color;
  }
}

// 函数
@function rem($pixels) {
  @return #{$pixels / 16}rem;
}

@function z-index($layer) {
  $z-indexes: (
    modal: 1000,
    dropdown: 100,
    header: 50,
    default: 1
  );

  @return map-get($z-indexes, $layer);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: z-index(modal);
  background-color: rgba(black, 0.5);
  @include flex-center;
}

// Less 示例
/*
// variables.less
@primary-color: #3b82f6;
@font-size-base: 1rem;
@border-radius: 0.5rem;

// mixins.less
.button-mixin(@bg-color) {
  background-color: @bg-color;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: @border-radius;
  cursor: pointer;

  &:hover {
    background-color: darken(@bg-color, 10%);
  }
}

// main.less
.button {
  .button-mixin(@primary-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;

  .header {
    background-color: @primary-color;

    h1 {
      color: white;
      font-size: @font-size-base * 2;
    }
  }
}
*/`,
      useCases: ['代码组织', '样式复用', '主题管理', '构建优化'],
      relatedTerms: ['sass', 'less', 'variables', 'mixins', 'nesting']
    }
  ],

  // 算法与数据结构 (60个术语)
  algorithms: [
    {
      id: 'algo_1',
      name: 'binary_search',
      chinese: '二分查找',
      description: '在有序数组中查找特定元素的高效算法，时间复杂度O(log n)',
      difficulty: 'intermediate',
      tags: ['search', 'efficiency', 'sorted'],
      example: `function binarySearch(arr, target) {
  let left = 0;
  let right = arr.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) {
      return mid; // 找到目标，返回索引
    } else if (arr[mid] < target) {
      left = mid + 1; // 目标在右半部分
    } else {
      right = mid - 1; // 目标在左半部分
    }
  }

  return -1; // 未找到目标
}

// 使用示例
const sortedArray = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
console.log(binarySearch(sortedArray, 7)); // 输出: 3
console.log(binarySearch(sortedArray, 6)); // 输出: -1`,
      useCases: ['搜索优化', '数据查找', '算法面试', '性能提升'],
      relatedTerms: ['sorting', 'time complexity', 'divide and conquer']
    },
    {
      id: 'algo_2',
      name: 'quicksort',
      chinese: '快速排序',
      description: '基于分治策略的高效排序算法，平均时间复杂度O(n log n)',
      difficulty: 'advanced',
      tags: ['sorting', 'divide-conquer', 'recursion'],
      example: `function quickSort(arr, low = 0, high = arr.length - 1) {
  if (low < high) {
    // 分区操作，返回基准元素的正确位置
    const pivotIndex = partition(arr, low, high);

    // 递归排序基准元素左右两部分
    quickSort(arr, low, pivotIndex - 1);
    quickSort(arr, pivotIndex + 1, high);
  }

  return arr;
}

function partition(arr, low, high) {
  // 选择最后一个元素作为基准
  const pivot = arr[high];
  let i = low - 1; // 较小元素的索引

  for (let j = low; j < high; j++) {
    // 如果当前元素小于或等于基准
    if (arr[j] <= pivot) {
      i++;
      [arr[i], arr[j]] = [arr[j], arr[i]]; // 交换元素
    }
  }

  // 将基准元素放到正确位置
  [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]];
  return i + 1;
}

// 使用示例
const numbers = [64, 34, 25, 12, 22, 11, 90];
console.log('原数组:', numbers);
console.log('排序后:', quickSort([...numbers]));

// 优化版本 - 随机选择基准
function quickSortOptimized(arr, low = 0, high = arr.length - 1) {
  if (low < high) {
    // 随机选择基准以避免最坏情况
    const randomIndex = Math.floor(Math.random() * (high - low + 1)) + low;
    [arr[randomIndex], arr[high]] = [arr[high], arr[randomIndex]];

    const pivotIndex = partition(arr, low, high);
    quickSortOptimized(arr, low, pivotIndex - 1);
    quickSortOptimized(arr, pivotIndex + 1, high);
  }

  return arr;
}`,
      useCases: ['数据排序', '算法竞赛', '系统排序', '性能优化'],
      relatedTerms: ['merge sort', 'partition', 'pivot', 'divide and conquer']
    },
    {
      id: 'algo_3',
      name: 'hash_table',
      chinese: '哈希表',
      description: '基于哈希函数的数据结构，提供O(1)平均时间复杂度的查找、插入和删除',
      difficulty: 'intermediate',
      tags: ['data-structure', 'hashing', 'performance'],
      example: `class HashTable {
  constructor(size = 53) {
    this.keyMap = new Array(size);
  }

  // 哈希函数
  _hash(key) {
    let total = 0;
    let WEIRD_PRIME = 31;
    for (let i = 0; i < Math.min(key.length, 100); i++) {
      let char = key[i];
      let value = char.charCodeAt(0) - 96;
      total = (total * WEIRD_PRIME + value) % this.keyMap.length;
    }
    return total;
  }

  // 设置键值对
  set(key, value) {
    let index = this._hash(key);
    if (!this.keyMap[index]) {
      this.keyMap[index] = [];
    }

    // 检查是否已存在该键
    for (let i = 0; i < this.keyMap[index].length; i++) {
      if (this.keyMap[index][i][0] === key) {
        this.keyMap[index][i][1] = value;
        return;
      }
    }

    // 添加新的键值对
    this.keyMap[index].push([key, value]);
  }

  // 获取值
  get(key) {
    let index = this._hash(key);
    if (this.keyMap[index]) {
      for (let i = 0; i < this.keyMap[index].length; i++) {
        if (this.keyMap[index][i][0] === key) {
          return this.keyMap[index][i][1];
        }
      }
    }
    return undefined;
  }

  // 删除键值对
  delete(key) {
    let index = this._hash(key);
    if (this.keyMap[index]) {
      for (let i = 0; i < this.keyMap[index].length; i++) {
        if (this.keyMap[index][i][0] === key) {
          return this.keyMap[index].splice(i, 1);
        }
      }
    }
    return undefined;
  }

  // 获取所有键
  keys() {
    let keysArr = [];
    for (let i = 0; i < this.keyMap.length; i++) {
      if (this.keyMap[i]) {
        for (let j = 0; j < this.keyMap[i].length; j++) {
          keysArr.push(this.keyMap[i][j][0]);
        }
      }
    }
    return keysArr;
  }

  // 获取所有值
  values() {
    let valuesArr = [];
    for (let i = 0; i < this.keyMap.length; i++) {
      if (this.keyMap[i]) {
        for (let j = 0; j < this.keyMap[i].length; j++) {
          if (!valuesArr.includes(this.keyMap[i][j][1])) {
            valuesArr.push(this.keyMap[i][j][1]);
          }
        }
      }
    }
    return valuesArr;
  }
}

// 使用示例
const ht = new HashTable();
ht.set("hello", "world");
ht.set("dogs", "are cool");
ht.set("cats", "are fine");
ht.set("i love", "pizza");

console.log(ht.get("hello")); // "world"
console.log(ht.keys()); // ["hello", "dogs", "cats", "i love"]
console.log(ht.values()); // ["world", "are cool", "are fine", "pizza"]`,
      useCases: ['缓存系统', '数据库索引', '快速查找', '去重操作'],
      relatedTerms: ['hash function', 'collision', 'load factor', 'dictionary']
    },
    {
      id: 'algo_4',
      name: 'binary_tree',
      chinese: '二叉树',
      description: '每个节点最多有两个子节点的树形数据结构，是许多高级数据结构的基础',
      difficulty: 'intermediate',
      tags: ['data-structure', 'tree', 'recursion'],
      example: `// 二叉树节点定义
class TreeNode {
  constructor(val, left = null, right = null) {
    this.val = val;
    this.left = left;
    this.right = right;
  }
}

// 二叉搜索树实现
class BinarySearchTree {
  constructor() {
    this.root = null;
  }

  // 插入节点
  insert(val) {
    this.root = this._insertNode(this.root, val);
  }

  _insertNode(node, val) {
    if (node === null) {
      return new TreeNode(val);
    }

    if (val < node.val) {
      node.left = this._insertNode(node.left, val);
    } else if (val > node.val) {
      node.right = this._insertNode(node.right, val);
    }

    return node;
  }

  // 搜索节点
  search(val) {
    return this._searchNode(this.root, val);
  }

  _searchNode(node, val) {
    if (node === null || node.val === val) {
      return node;
    }

    if (val < node.val) {
      return this._searchNode(node.left, val);
    } else {
      return this._searchNode(node.right, val);
    }
  }

  // 删除节点
  delete(val) {
    this.root = this._deleteNode(this.root, val);
  }

  _deleteNode(node, val) {
    if (node === null) return null;

    if (val < node.val) {
      node.left = this._deleteNode(node.left, val);
    } else if (val > node.val) {
      node.right = this._deleteNode(node.right, val);
    } else {
      // 找到要删除的节点
      if (node.left === null) return node.right;
      if (node.right === null) return node.left;

      // 有两个子节点：找到右子树的最小值
      let minNode = this._findMin(node.right);
      node.val = minNode.val;
      node.right = this._deleteNode(node.right, minNode.val);
    }

    return node;
  }

  _findMin(node) {
    while (node.left !== null) {
      node = node.left;
    }
    return node;
  }

  // 遍历方法
  inorderTraversal() {
    const result = [];
    this._inorder(this.root, result);
    return result;
  }

  _inorder(node, result) {
    if (node !== null) {
      this._inorder(node.left, result);
      result.push(node.val);
      this._inorder(node.right, result);
    }
  }

  preorderTraversal() {
    const result = [];
    this._preorder(this.root, result);
    return result;
  }

  _preorder(node, result) {
    if (node !== null) {
      result.push(node.val);
      this._preorder(node.left, result);
      this._preorder(node.right, result);
    }
  }

  postorderTraversal() {
    const result = [];
    this._postorder(this.root, result);
    return result;
  }

  _postorder(node, result) {
    if (node !== null) {
      this._postorder(node.left, result);
      this._postorder(node.right, result);
      result.push(node.val);
    }
  }

  // 层序遍历（广度优先）
  levelOrderTraversal() {
    if (!this.root) return [];

    const result = [];
    const queue = [this.root];

    while (queue.length > 0) {
      const node = queue.shift();
      result.push(node.val);

      if (node.left) queue.push(node.left);
      if (node.right) queue.push(node.right);
    }

    return result;
  }

  // 计算树的高度
  height() {
    return this._calculateHeight(this.root);
  }

  _calculateHeight(node) {
    if (node === null) return -1;

    const leftHeight = this._calculateHeight(node.left);
    const rightHeight = this._calculateHeight(node.right);

    return Math.max(leftHeight, rightHeight) + 1;
  }
}

// 使用示例
const bst = new BinarySearchTree();
[50, 30, 70, 20, 40, 60, 80].forEach(val => bst.insert(val));

console.log('中序遍历:', bst.inorderTraversal()); // [20, 30, 40, 50, 60, 70, 80]
console.log('前序遍历:', bst.preorderTraversal()); // [50, 30, 20, 40, 70, 60, 80]
console.log('后序遍历:', bst.postorderTraversal()); // [20, 40, 30, 60, 80, 70, 50]
console.log('层序遍历:', bst.levelOrderTraversal()); // [50, 30, 70, 20, 40, 60, 80]
console.log('树的高度:', bst.height()); // 2`,
      useCases: ['搜索优化', '排序算法', '表达式解析', '文件系统'],
      relatedTerms: ['recursion', 'traversal', 'binary search', 'tree algorithms']
    },
    {
      id: 'algo_5',
      name: 'dynamic_programming',
      chinese: '动态规划',
      description: '通过将复杂问题分解为子问题并存储子问题解的算法设计技术',
      difficulty: 'advanced',
      tags: ['optimization', 'memoization', 'recursion'],
      example: `// 1. 斐波那契数列 - 基础动态规划
function fibonacciDP(n) {
  if (n <= 1) return n;

  const dp = new Array(n + 1);
  dp[0] = 0;
  dp[1] = 1;

  for (let i = 2; i <= n; i++) {
    dp[i] = dp[i - 1] + dp[i - 2];
  }

  return dp[n];
}

// 空间优化版本
function fibonacciOptimized(n) {
  if (n <= 1) return n;

  let prev2 = 0, prev1 = 1;

  for (let i = 2; i <= n; i++) {
    const current = prev1 + prev2;
    prev2 = prev1;
    prev1 = current;
  }

  return prev1;
}

// 2. 最长公共子序列 (LCS)
function longestCommonSubsequence(text1, text2) {
  const m = text1.length;
  const n = text2.length;
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (text1[i - 1] === text2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
      } else {
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
      }
    }
  }

  return dp[m][n];
}

// 3. 0-1背包问题
function knapsack(weights, values, capacity) {
  const n = weights.length;
  const dp = Array(n + 1).fill().map(() => Array(capacity + 1).fill(0));

  for (let i = 1; i <= n; i++) {
    for (let w = 1; w <= capacity; w++) {
      if (weights[i - 1] <= w) {
        dp[i][w] = Math.max(
          dp[i - 1][w], // 不选择当前物品
          dp[i - 1][w - weights[i - 1]] + values[i - 1] // 选择当前物品
        );
      } else {
        dp[i][w] = dp[i - 1][w];
      }
    }
  }

  return dp[n][capacity];
}

// 4. 最长递增子序列 (LIS)
function lengthOfLIS(nums) {
  if (nums.length === 0) return 0;

  const dp = new Array(nums.length).fill(1);

  for (let i = 1; i < nums.length; i++) {
    for (let j = 0; j < i; j++) {
      if (nums[j] < nums[i]) {
        dp[i] = Math.max(dp[i], dp[j] + 1);
      }
    }
  }

  return Math.max(...dp);
}

// 5. 编辑距离 (Levenshtein Distance)
function editDistance(word1, word2) {
  const m = word1.length;
  const n = word2.length;
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // 初始化边界条件
  for (let i = 0; i <= m; i++) dp[i][0] = i;
  for (let j = 0; j <= n; j++) dp[0][j] = j;

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (word1[i - 1] === word2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1];
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,     // 删除
          dp[i][j - 1] + 1,     // 插入
          dp[i - 1][j - 1] + 1  // 替换
        );
      }
    }
  }

  return dp[m][n];
}

// 6. 硬币找零问题
function coinChange(coins, amount) {
  const dp = new Array(amount + 1).fill(Infinity);
  dp[0] = 0;

  for (let i = 1; i <= amount; i++) {
    for (const coin of coins) {
      if (coin <= i) {
        dp[i] = Math.min(dp[i], dp[i - coin] + 1);
      }
    }
  }

  return dp[amount] === Infinity ? -1 : dp[amount];
}

// 7. 记忆化递归 (Memoization)
function climbStairs(n, memo = {}) {
  if (n in memo) return memo[n];
  if (n <= 2) return n;

  memo[n] = climbStairs(n - 1, memo) + climbStairs(n - 2, memo);
  return memo[n];
}

// 使用示例
console.log('斐波那契数列第10项:', fibonacciDP(10)); // 55
console.log('LCS长度:', longestCommonSubsequence("abcde", "ace")); // 3
console.log('背包最大价值:', knapsack([1, 3, 4, 5], [1, 4, 5, 7], 7)); // 9
console.log('最长递增子序列:', lengthOfLIS([10, 9, 2, 5, 3, 7, 101, 18])); // 4
console.log('编辑距离:', editDistance("horse", "ros")); // 3
console.log('最少硬币数:', coinChange([1, 3, 4], 6)); // 2`,
      useCases: ['优化问题', '路径规划', '资源分配', '序列分析'],
      relatedTerms: ['memoization', 'optimization', 'recursion', 'subproblems']
    },
    {
      id: 'algo_6',
      name: 'graph_algorithms',
      chinese: '图算法',
      description: '处理图数据结构的算法，包括遍历、最短路径、最小生成树等',
      difficulty: 'advanced',
      tags: ['graph', 'traversal', 'shortest-path'],
      example: `// 图的表示
class Graph {
  constructor() {
    this.adjacencyList = {};
  }

  addVertex(vertex) {
    if (!this.adjacencyList[vertex]) {
      this.adjacencyList[vertex] = [];
    }
  }

  addEdge(v1, v2, weight = 1) {
    this.adjacencyList[v1].push({ node: v2, weight });
    this.adjacencyList[v2].push({ node: v1, weight }); // 无向图
  }

  // 深度优先搜索 (DFS)
  dfs(start) {
    const result = [];
    const visited = {};

    const dfsHelper = (vertex) => {
      visited[vertex] = true;
      result.push(vertex);

      this.adjacencyList[vertex].forEach(neighbor => {
        if (!visited[neighbor.node]) {
          dfsHelper(neighbor.node);
        }
      });
    };

    dfsHelper(start);
    return result;
  }

  // 广度优先搜索 (BFS)
  bfs(start) {
    const queue = [start];
    const result = [];
    const visited = {};
    visited[start] = true;

    while (queue.length) {
      const vertex = queue.shift();
      result.push(vertex);

      this.adjacencyList[vertex].forEach(neighbor => {
        if (!visited[neighbor.node]) {
          visited[neighbor.node] = true;
          queue.push(neighbor.node);
        }
      });
    }

    return result;
  }

  // Dijkstra最短路径算法
  dijkstra(start, end) {
    const distances = {};
    const previous = {};
    const pq = new PriorityQueue();

    // 初始化距离
    for (let vertex in this.adjacencyList) {
      if (vertex === start) {
        distances[vertex] = 0;
        pq.enqueue(vertex, 0);
      } else {
        distances[vertex] = Infinity;
        pq.enqueue(vertex, Infinity);
      }
      previous[vertex] = null;
    }

    while (pq.values.length) {
      const smallest = pq.dequeue().val;

      if (smallest === end) {
        // 构建路径
        const path = [];
        let current = end;
        while (previous[current]) {
          path.push(current);
          current = previous[current];
        }
        path.push(start);
        return {
          distance: distances[end],
          path: path.reverse()
        };
      }

      if (distances[smallest] !== Infinity) {
        this.adjacencyList[smallest].forEach(neighbor => {
          const candidate = distances[smallest] + neighbor.weight;

          if (candidate < distances[neighbor.node]) {
            distances[neighbor.node] = candidate;
            previous[neighbor.node] = smallest;
            pq.enqueue(neighbor.node, candidate);
          }
        });
      }
    }
  }
}

// 优先队列实现
class PriorityQueue {
  constructor() {
    this.values = [];
  }

  enqueue(val, priority) {
    this.values.push({ val, priority });
    this.sort();
  }

  dequeue() {
    return this.values.shift();
  }

  sort() {
    this.values.sort((a, b) => a.priority - b.priority);
  }
}

// 拓扑排序
function topologicalSort(graph) {
  const inDegree = {};
  const queue = [];
  const result = [];

  // 计算入度
  for (let vertex in graph) {
    inDegree[vertex] = 0;
  }

  for (let vertex in graph) {
    graph[vertex].forEach(neighbor => {
      inDegree[neighbor]++;
    });
  }

  // 找到入度为0的节点
  for (let vertex in inDegree) {
    if (inDegree[vertex] === 0) {
      queue.push(vertex);
    }
  }

  while (queue.length) {
    const vertex = queue.shift();
    result.push(vertex);

    graph[vertex].forEach(neighbor => {
      inDegree[neighbor]--;
      if (inDegree[neighbor] === 0) {
        queue.push(neighbor);
      }
    });
  }

  return result.length === Object.keys(graph).length ? result : null;
}

// 检测环
function hasCycle(graph) {
  const visited = {};
  const recStack = {};

  const dfsHelper = (vertex) => {
    visited[vertex] = true;
    recStack[vertex] = true;

    for (let neighbor of graph[vertex] || []) {
      if (!visited[neighbor] && dfsHelper(neighbor)) {
        return true;
      } else if (recStack[neighbor]) {
        return true;
      }
    }

    recStack[vertex] = false;
    return false;
  };

  for (let vertex in graph) {
    if (!visited[vertex] && dfsHelper(vertex)) {
      return true;
    }
  }

  return false;
}

// 使用示例
const graph = new Graph();
['A', 'B', 'C', 'D', 'E', 'F'].forEach(v => graph.addVertex(v));
graph.addEdge('A', 'B', 4);
graph.addEdge('A', 'C', 2);
graph.addEdge('B', 'E', 3);
graph.addEdge('C', 'D', 2);
graph.addEdge('C', 'F', 4);
graph.addEdge('D', 'E', 3);
graph.addEdge('D', 'F', 1);
graph.addEdge('E', 'F', 1);

console.log('DFS遍历:', graph.dfs('A'));
console.log('BFS遍历:', graph.bfs('A'));
console.log('最短路径:', graph.dijkstra('A', 'E'));`,
      useCases: ['社交网络', '路径规划', '网络分析', '依赖管理'],
      relatedTerms: ['dfs', 'bfs', 'dijkstra', 'topological sort']
    }
  ],

  // Web安全 (40个术语)
  security: [
    {
      id: 'sec_1',
      name: 'xss',
      chinese: '跨站脚本攻击',
      description: '恶意脚本注入到可信网站的安全漏洞',
      difficulty: 'advanced',
      tags: ['security', 'injection', 'client-side'],
      example: `// 危险代码 - 容易受到XSS攻击
function displayUserComment(comment) {
  document.getElementById('comments').innerHTML = comment;
  // 如果comment包含<script>alert('XSS')</script>，会执行恶意代码
}

// 安全代码 - 防范XSS攻击
function displayUserCommentSafe(comment) {
  // 方法1: 使用textContent而不是innerHTML
  const commentElement = document.createElement('div');
  commentElement.textContent = comment;
  document.getElementById('comments').appendChild(commentElement);

  // 方法2: 使用DOMPurify库清理输入
  const cleanComment = DOMPurify.sanitize(comment);
  document.getElementById('comments').innerHTML = cleanComment;
}

// 后端防护
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"]
    }
  }
}));`,
      useCases: ['Web安全', '用户输入验证', '内容过滤', '安全编码'],
      relatedTerms: ['csrf', 'sql injection', 'sanitization']
    },
    {
      id: 'sec_2',
      name: 'csrf',
      chinese: '跨站请求伪造',
      description: '攻击者诱导用户在已认证的网站上执行非预期操作的攻击方式',
      difficulty: 'advanced',
      tags: ['security', 'authentication', 'tokens'],
      example: `// CSRF攻击示例
// 恶意网站包含的表单
/*
<form action="https://bank.com/transfer" method="POST">
  <input type="hidden" name="to" value="attacker_account">
  <input type="hidden" name="amount" value="1000">
  <input type="submit" value="点击领取奖品">
</form>
*/

// 防护措施1: CSRF Token
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });

app.use(csrfProtection);

app.get('/form', (req, res) => {
  res.render('form', { csrfToken: req.csrfToken() });
});

// 前端表单包含CSRF token
/*
<form action="/transfer" method="POST">
  <input type="hidden" name="_csrf" value="{{csrfToken}}">
  <input type="text" name="to" placeholder="收款账户">
  <input type="number" name="amount" placeholder="金额">
  <button type="submit">转账</button>
</form>
*/

// 防护措施2: SameSite Cookie
app.use(session({
  secret: 'your-secret-key',
  cookie: {
    sameSite: 'strict', // 或 'lax'
    secure: true, // HTTPS环境
    httpOnly: true
  }
}));

// 防护措施3: 验证Referer头
function checkReferer(req, res, next) {
  const referer = req.get('Referer');
  const host = req.get('Host');

  if (!referer || !referer.includes(host)) {
    return res.status(403).json({ error: 'Invalid referer' });
  }

  next();
}

app.post('/sensitive-action', checkReferer, (req, res) => {
  // 处理敏感操作
});

// 防护措施4: 双重提交Cookie
function generateCSRFToken() {
  return crypto.randomBytes(32).toString('hex');
}

app.use((req, res, next) => {
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateCSRFToken();
  }
  res.cookie('csrf-token', req.session.csrfToken);
  next();
});`,
      useCases: ['表单保护', '状态改变操作', '金融交易', 'API安全'],
      relatedTerms: ['xss', 'authentication', 'session', 'tokens']
    },
    {
      id: 'sec_3',
      name: 'jwt',
      chinese: 'JSON Web Token',
      description: '用于安全传输信息的开放标准，常用于身份验证和信息交换',
      difficulty: 'intermediate',
      tags: ['authentication', 'tokens', 'stateless'],
      example: `const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// 用户登录
app.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证用户
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // 生成JWT
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const accessToken = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: '7d' }
    );

    // 存储refresh token
    await user.update({ refreshToken });

    res.json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name
      }
    });

  } catch (error) {
    res.status(500).json({ error: 'Login failed' });
  }
});

// JWT验证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }

    req.user = user;
    next();
  });
}

// 刷新token
app.post('/refresh', async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({ error: 'Refresh token required' });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user || user.refreshToken !== refreshToken) {
      return res.status(403).json({ error: 'Invalid refresh token' });
    }

    const newAccessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );

    res.json({ accessToken: newAccessToken });

  } catch (error) {
    res.status(403).json({ error: 'Invalid refresh token' });
  }
});

// 使用认证中间件保护路由
app.get('/profile', authenticateToken, (req, res) => {
  res.json({ user: req.user });
});`,
      useCases: ['用户认证', 'API授权', '单点登录', '微服务通信'],
      relatedTerms: ['authentication', 'authorization', 'sessions', 'oauth']
    },
    {
      id: 'sec_4',
      name: 'oauth',
      chinese: 'OAuth授权',
      description: '开放授权标准，允许第三方应用访问用户资源而无需暴露密码',
      difficulty: 'advanced',
      tags: ['authentication', 'authorization', 'oauth'],
      example: `// OAuth 2.0 授权码流程实现
const express = require('express');
const axios = require('axios');
const crypto = require('crypto');

const app = express();

// OAuth配置
const oauthConfig = {
  clientId: process.env.OAUTH_CLIENT_ID,
  clientSecret: process.env.OAUTH_CLIENT_SECRET,
  redirectUri: 'http://localhost:3000/auth/callback',
  authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
  tokenUrl: 'https://oauth2.googleapis.com/token',
  userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
  scope: 'openid email profile'
};

// 生成随机state参数防止CSRF攻击
function generateState() {
  return crypto.randomBytes(32).toString('hex');
}

// 步骤1: 重定向到授权服务器
app.get('/auth/login', (req, res) => {
  const state = generateState();
  req.session.oauthState = state;

  const authUrl = new URL(oauthConfig.authorizationUrl);
  authUrl.searchParams.append('client_id', oauthConfig.clientId);
  authUrl.searchParams.append('redirect_uri', oauthConfig.redirectUri);
  authUrl.searchParams.append('response_type', 'code');
  authUrl.searchParams.append('scope', oauthConfig.scope);
  authUrl.searchParams.append('state', state);

  res.redirect(authUrl.toString());
});

// 步骤2: 处理授权回调
app.get('/auth/callback', async (req, res) => {
  const { code, state, error } = req.query;

  // 检查错误
  if (error) {
    return res.status(400).json({ error: 'Authorization failed', details: error });
  }

  // 验证state参数
  if (state !== req.session.oauthState) {
    return res.status(400).json({ error: 'Invalid state parameter' });
  }

  try {
    // 步骤3: 交换授权码获取访问令牌
    const tokenResponse = await axios.post(oauthConfig.tokenUrl, {
      client_id: oauthConfig.clientId,
      client_secret: oauthConfig.clientSecret,
      code: code,
      grant_type: 'authorization_code',
      redirect_uri: oauthConfig.redirectUri
    });

    const { access_token, refresh_token, expires_in } = tokenResponse.data;

    // 步骤4: 使用访问令牌获取用户信息
    const userResponse = await axios.get(oauthConfig.userInfoUrl, {
      headers: {
        Authorization: \`Bearer \${access_token}\`
      }
    });

    const userInfo = userResponse.data;

    // 存储用户信息和令牌
    req.session.user = {
      id: userInfo.id,
      email: userInfo.email,
      name: userInfo.name,
      picture: userInfo.picture
    };

    req.session.tokens = {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiresAt: Date.now() + (expires_in * 1000)
    };

    res.redirect('/dashboard');

  } catch (error) {
    console.error('OAuth error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// 刷新访问令牌
async function refreshAccessToken(refreshToken) {
  try {
    const response = await axios.post(oauthConfig.tokenUrl, {
      client_id: oauthConfig.clientId,
      client_secret: oauthConfig.clientSecret,
      refresh_token: refreshToken,
      grant_type: 'refresh_token'
    });

    return response.data;
  } catch (error) {
    throw new Error('Failed to refresh token');
  }
}

// 中间件：检查认证状态
async function requireAuth(req, res, next) {
  if (!req.session.user || !req.session.tokens) {
    return res.redirect('/auth/login');
  }

  // 检查令牌是否过期
  if (Date.now() >= req.session.tokens.expiresAt) {
    try {
      const newTokens = await refreshAccessToken(req.session.tokens.refreshToken);
      req.session.tokens = {
        accessToken: newTokens.access_token,
        refreshToken: newTokens.refresh_token || req.session.tokens.refreshToken,
        expiresAt: Date.now() + (newTokens.expires_in * 1000)
      };
    } catch (error) {
      req.session.destroy();
      return res.redirect('/auth/login');
    }
  }

  next();
}

// 受保护的路由
app.get('/dashboard', requireAuth, (req, res) => {
  res.json({
    message: 'Welcome to dashboard',
    user: req.session.user
  });
});

// 注销
app.get('/auth/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/');
});

// PKCE (Proof Key for Code Exchange) 实现
function generateCodeVerifier() {
  return crypto.randomBytes(32).toString('base64url');
}

function generateCodeChallenge(verifier) {
  return crypto.createHash('sha256').update(verifier).digest('base64url');
}

// 使用PKCE的OAuth流程（更安全，适用于SPA）
app.get('/auth/pkce/login', (req, res) => {
  const state = generateState();
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);

  req.session.oauthState = state;
  req.session.codeVerifier = codeVerifier;

  const authUrl = new URL(oauthConfig.authorizationUrl);
  authUrl.searchParams.append('client_id', oauthConfig.clientId);
  authUrl.searchParams.append('redirect_uri', oauthConfig.redirectUri);
  authUrl.searchParams.append('response_type', 'code');
  authUrl.searchParams.append('scope', oauthConfig.scope);
  authUrl.searchParams.append('state', state);
  authUrl.searchParams.append('code_challenge', codeChallenge);
  authUrl.searchParams.append('code_challenge_method', 'S256');

  res.redirect(authUrl.toString());
});

// 前端JavaScript OAuth实现
/*
class OAuthClient {
  constructor(config) {
    this.config = config;
  }

  // 生成授权URL
  getAuthorizationUrl() {
    const state = this.generateRandomString();
    const codeVerifier = this.generateRandomString();
    const codeChallenge = this.generateCodeChallenge(codeVerifier);

    localStorage.setItem('oauth_state', state);
    localStorage.setItem('code_verifier', codeVerifier);

    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      response_type: 'code',
      scope: this.config.scope,
      state: state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    });

    return \`\${this.config.authorizationUrl}?\${params}\`;
  }

  // 处理授权回调
  async handleCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (state !== localStorage.getItem('oauth_state')) {
      throw new Error('Invalid state parameter');
    }

    const codeVerifier = localStorage.getItem('code_verifier');

    const tokenResponse = await fetch('/api/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        code,
        code_verifier: codeVerifier
      })
    });

    const tokens = await tokenResponse.json();
    localStorage.setItem('access_token', tokens.access_token);
    localStorage.setItem('refresh_token', tokens.refresh_token);

    return tokens;
  }

  generateRandomString() {
    const array = new Uint32Array(28);
    crypto.getRandomValues(array);
    return Array.from(array, dec => ('0' + dec.toString(16)).substr(-2)).join('');
  }

  generateCodeChallenge(verifier) {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    return crypto.subtle.digest('SHA-256', data).then(digest => {
      return btoa(String.fromCharCode(...new Uint8Array(digest)))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
    });
  }
}
*/`,
      useCases: ['第三方登录', 'API授权', '单点登录', '移动应用认证'],
      relatedTerms: ['jwt', 'pkce', 'authorization code', 'access token']
    },
    {
      id: 'sec_5',
      name: 'https_ssl',
      chinese: 'HTTPS/SSL',
      description: '安全超文本传输协议，通过SSL/TLS加密保护数据传输安全',
      difficulty: 'intermediate',
      tags: ['encryption', 'security', 'certificates'],
      example: `// Node.js HTTPS服务器设置
const https = require('https');
const fs = require('fs');
const express = require('express');

const app = express();

// 读取SSL证书
const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem'),
  ca: fs.readFileSync('path/to/ca-certificate.pem') // 可选，中间证书
};

// 创建HTTPS服务器
const httpsServer = https.createServer(options, app);

// 强制HTTPS重定向中间件
function forceHTTPS(req, res, next) {
  if (!req.secure && req.get('x-forwarded-proto') !== 'https') {
    return res.redirect(301, \`https://\${req.get('host')}\${req.url}\`);
  }
  next();
}

// 安全头部中间件
function securityHeaders(req, res, next) {
  // HSTS (HTTP Strict Transport Security)
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // 防止内容类型嗅探
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // XSS保护
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // 防止点击劫持
  res.setHeader('X-Frame-Options', 'DENY');

  // CSP (Content Security Policy)
  res.setHeader('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self' https:; " +
    "connect-src 'self' https:; " +
    "frame-ancestors 'none';"
  );

  // 引用者策略
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // 权限策略
  res.setHeader('Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=()'
  );

  next();
}

app.use(forceHTTPS);
app.use(securityHeaders);

// 证书验证
function validateCertificate(cert) {
  const now = new Date();
  const notBefore = new Date(cert.valid_from);
  const notAfter = new Date(cert.valid_to);

  if (now < notBefore || now > notAfter) {
    throw new Error('Certificate is not valid');
  }

  // 检查证书链
  if (!cert.issuer || !cert.subject) {
    throw new Error('Invalid certificate format');
  }

  return true;
}

// 客户端证书验证
const clientCertOptions = {
  key: fs.readFileSync('server-key.pem'),
  cert: fs.readFileSync('server-cert.pem'),
  ca: fs.readFileSync('ca-cert.pem'),
  requestCert: true,
  rejectUnauthorized: true
};

const secureServer = https.createServer(clientCertOptions, (req, res) => {
  const cert = req.connection.getPeerCertificate();

  if (req.client.authorized) {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      message: 'Authenticated with client certificate',
      subject: cert.subject,
      issuer: cert.issuer,
      valid_from: cert.valid_from,
      valid_to: cert.valid_to
    }));
  } else {
    res.writeHead(401);
    res.end('Client certificate required');
  }
});

// Let's Encrypt 自动证书管理
const acme = require('acme-client');

async function obtainCertificate(domain) {
  const accountKey = await acme.crypto.createPrivateKey();
  const client = new acme.Client({
    directoryUrl: acme.directory.letsencrypt.production,
    accountKey
  });

  const [key, csr] = await acme.crypto.createCsr({
    commonName: domain,
    altNames: [\`www.\${domain}\`]
  });

  const cert = await client.auto({
    csr,
    email: '<EMAIL>',
    termsOfServiceAgreed: true,
    challengeCreateFn: async (authz, challenge, keyAuthorization) => {
      // 实现HTTP-01或DNS-01挑战
      console.log('Challenge:', challenge);
    },
    challengeRemoveFn: async (authz, challenge, keyAuthorization) => {
      // 清理挑战
    }
  });

  return { key, cert };
}

// 证书自动续期
async function renewCertificate() {
  try {
    const { key, cert } = await obtainCertificate('example.com');

    // 保存新证书
    fs.writeFileSync('new-private-key.pem', key);
    fs.writeFileSync('new-certificate.pem', cert);

    // 重新加载服务器（零停机时间）
    process.kill(process.pid, 'SIGUSR2');

  } catch (error) {
    console.error('Certificate renewal failed:', error);
  }
}

// 定期检查证书过期
setInterval(() => {
  const cert = fs.readFileSync('certificate.pem');
  const certInfo = require('crypto').createHash('sha256').update(cert).digest('hex');

  // 检查证书是否在30天内过期
  const expiryDate = new Date(/* 从证书中解析过期时间 */);
  const daysUntilExpiry = (expiryDate - new Date()) / (1000 * 60 * 60 * 24);

  if (daysUntilExpiry < 30) {
    renewCertificate();
  }
}, 24 * 60 * 60 * 1000); // 每天检查一次

// 启动服务器
httpsServer.listen(443, () => {
  console.log('HTTPS Server running on port 443');
});

// HTTP到HTTPS重定向服务器
const http = require('http');
const httpServer = http.createServer((req, res) => {
  res.writeHead(301, {
    Location: \`https://\${req.headers.host}\${req.url}\`
  });
  res.end();
});

httpServer.listen(80, () => {
  console.log('HTTP redirect server running on port 80');
});`,
      useCases: ['数据加密', '身份验证', '网站安全', '合规要求'],
      relatedTerms: ['tls', 'certificates', 'encryption', 'hsts']
    }
  ],

  // 新兴技术 (50个术语)
  emerging: [
    {
      id: 'emerging_1',
      name: 'webassembly',
      chinese: 'Web汇编',
      description: '在Web浏览器中运行接近原生性能代码的二进制指令格式',
      difficulty: 'advanced',
      tags: ['performance', 'compilation', 'native'],
      example: `// Rust代码编译为WebAssembly
#[wasm_bindgen]
pub fn fibonacci(n: u32) -> u32 {
    match n {
        0 => 0,
        1 => 1,
        _ => fibonacci(n - 1) + fibonacci(n - 2)
    }
}

// JavaScript中使用WebAssembly
async function loadWasm() {
  const wasm = await import('./pkg/fibonacci_wasm.js');
  await wasm.default();

  // 调用WebAssembly函数
  const result = wasm.fibonacci(40);
  console.log(\`Fibonacci(40) = \${result}\`);

  // 性能对比
  console.time('WASM');
  wasm.fibonacci(40);
  console.timeEnd('WASM');

  console.time('JavaScript');
  fibonacciJS(40);
  console.timeEnd('JavaScript');
}`,
      useCases: ['高性能计算', '游戏开发', '图像处理', '科学计算'],
      relatedTerms: ['performance', 'compilation', 'rust', 'c++']
    },
    {
      id: 'emerging_2',
      name: 'graphql',
      chinese: 'GraphQL查询语言',
      description: 'API的查询语言和运行时，提供更高效、强大和灵活的数据获取方式',
      difficulty: 'intermediate',
      tags: ['api', 'query', 'data-fetching'],
      example: `// GraphQL Schema定义
const typeDefs = \`
  type User {
    id: ID!
    name: String!
    email: String!
    posts: [Post!]!
  }

  type Post {
    id: ID!
    title: String!
    content: String!
    author: User!
    comments: [Comment!]!
  }

  type Comment {
    id: ID!
    text: String!
    author: User!
  }

  type Query {
    user(id: ID!): User
    users: [User!]!
    post(id: ID!): Post
    posts: [Post!]!
  }

  type Mutation {
    createUser(name: String!, email: String!): User!
    createPost(title: String!, content: String!, authorId: ID!): Post!
    updatePost(id: ID!, title: String, content: String): Post!
    deletePost(id: ID!): Boolean!
  }

  type Subscription {
    postAdded: Post!
    commentAdded(postId: ID!): Comment!
  }
\`;

// Resolvers
const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      return await User.findById(id);
    },
    users: async () => {
      return await User.findAll();
    },
    post: async (parent, { id }) => {
      return await Post.findById(id);
    },
    posts: async () => {
      return await Post.findAll();
    }
  },

  Mutation: {
    createUser: async (parent, { name, email }) => {
      return await User.create({ name, email });
    },
    createPost: async (parent, { title, content, authorId }) => {
      return await Post.create({ title, content, authorId });
    }
  },

  User: {
    posts: async (user) => {
      return await Post.findByAuthorId(user.id);
    }
  },

  Post: {
    author: async (post) => {
      return await User.findById(post.authorId);
    },
    comments: async (post) => {
      return await Comment.findByPostId(post.id);
    }
  }
};

// 客户端查询示例
const GET_USER_WITH_POSTS = \`
  query GetUserWithPosts($userId: ID!) {
    user(id: $userId) {
      id
      name
      email
      posts {
        id
        title
        content
        comments {
          id
          text
          author {
            name
          }
        }
      }
    }
  }
\`;

// 使用Apollo Client
import { useQuery, useMutation } from '@apollo/client';

function UserProfile({ userId }) {
  const { loading, error, data } = useQuery(GET_USER_WITH_POSTS, {
    variables: { userId }
  });

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>{data.user.name}</h1>
      <p>{data.user.email}</p>
      <div>
        {data.user.posts.map(post => (
          <div key={post.id}>
            <h3>{post.title}</h3>
            <p>{post.content}</p>
          </div>
        ))}
      </div>
    </div>
  );
}`,
      useCases: ['API设计', '数据获取优化', '类型安全', '实时订阅'],
      relatedTerms: ['rest api', 'apollo', 'schema', 'resolvers']
    },
    {
      id: 'emerging_3',
      name: 'microservices',
      chinese: '微服务架构',
      description: '将单体应用拆分为多个小型、独立服务的架构模式',
      difficulty: 'advanced',
      tags: ['architecture', 'scalability', 'distributed'],
      example: `// 微服务架构示例

// 用户服务 (User Service)
const express = require('express');
const userApp = express();

userApp.get('/users/:id', async (req, res) => {
  try {
    const user = await getUserById(req.params.id);
    res.json(user);
  } catch (error) {
    res.status(404).json({ error: 'User not found' });
  }
});

userApp.post('/users', async (req, res) => {
  try {
    const user = await createUser(req.body);
    res.status(201).json(user);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// 订单服务 (Order Service)
const orderApp = express();

orderApp.get('/orders/:id', async (req, res) => {
  try {
    const order = await getOrderById(req.params.id);

    // 调用用户服务获取用户信息
    const userResponse = await fetch(\`http://user-service:3001/users/\${order.userId}\`);
    const user = await userResponse.json();

    res.json({ ...order, user });
  } catch (error) {
    res.status(404).json({ error: 'Order not found' });
  }
});

// API网关 (API Gateway)
const gateway = express();
const httpProxy = require('http-proxy-middleware');

// 路由配置
const services = {
  '/api/users': 'http://user-service:3001',
  '/api/orders': 'http://order-service:3002',
  '/api/products': 'http://product-service:3003',
  '/api/payments': 'http://payment-service:3004'
};

// 设置代理
Object.entries(services).forEach(([path, target]) => {
  gateway.use(path, httpProxy({
    target,
    changeOrigin: true,
    pathRewrite: {
      [\`^\${path}\`]: ''
    }
  }));
});

// 服务发现
class ServiceRegistry {
  constructor() {
    this.services = new Map();
  }

  register(serviceName, serviceUrl, healthCheckUrl) {
    this.services.set(serviceName, {
      url: serviceUrl,
      healthCheck: healthCheckUrl,
      lastSeen: Date.now(),
      healthy: true
    });
  }

  discover(serviceName) {
    const service = this.services.get(serviceName);
    return service?.healthy ? service.url : null;
  }

  async healthCheck() {
    for (const [name, service] of this.services) {
      try {
        const response = await fetch(service.healthCheck);
        service.healthy = response.ok;
        service.lastSeen = Date.now();
      } catch (error) {
        service.healthy = false;
      }
    }
  }
}

// 断路器模式
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.threshold = threshold;
    this.timeout = timeout;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
  }

  async call(fn) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }

  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}

// 使用断路器
const userServiceBreaker = new CircuitBreaker();

async function getUserWithCircuitBreaker(userId) {
  return userServiceBreaker.call(async () => {
    const response = await fetch(\`http://user-service:3001/users/\${userId}\`);
    if (!response.ok) throw new Error('User service error');
    return response.json();
  });
}

// Docker Compose 配置
/*
version: '3.8'
services:
  api-gateway:
    build: ./api-gateway
    ports:
      - "3000:3000"
    depends_on:
      - user-service
      - order-service
    environment:
      - NODE_ENV=production

  user-service:
    build: ./user-service
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=mongodb://mongo:27017/users
    depends_on:
      - mongo

  order-service:
    build: ./order-service
    ports:
      - "3002:3002"
    environment:
      - DATABASE_URL=********************************************/orders
    depends_on:
      - postgres

  mongo:
    image: mongo:latest
    volumes:
      - mongo_data:/data/db

  postgres:
    image: postgres:latest
    environment:
      - POSTGRES_DB=orders
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  mongo_data:
  postgres_data:
*/

// Kubernetes 部署配置
/*
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        ports:
        - containerPort: 3001
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
spec:
  selector:
    app: user-service
  ports:
  - port: 3001
    targetPort: 3001
  type: ClusterIP
*/`,
      useCases: ['大型应用', '团队协作', '独立部署', '技术多样性'],
      relatedTerms: ['api gateway', 'service discovery', 'circuit breaker', 'containerization']
    },
    {
      id: 'emerging_4',
      name: 'serverless',
      chinese: '无服务器架构',
      description: '基于事件驱动的计算模型，开发者无需管理服务器基础设施',
      difficulty: 'intermediate',
      tags: ['serverless', 'cloud', 'functions'],
      example: `// AWS Lambda 函数示例
exports.handler = async (event, context) => {
  try {
    // 解析事件数据
    const { httpMethod, path, body, headers } = event;

    // 处理不同的HTTP方法
    switch (httpMethod) {
      case 'GET':
        return await handleGet(event);
      case 'POST':
        return await handlePost(JSON.parse(body));
      case 'PUT':
        return await handlePut(event.pathParameters.id, JSON.parse(body));
      case 'DELETE':
        return await handleDelete(event.pathParameters.id);
      default:
        return {
          statusCode: 405,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('Lambda error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};

async function handleGet(event) {
  const { id } = event.pathParameters || {};

  if (id) {
    // 获取单个资源
    const item = await getItemById(id);
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(item)
    };
  } else {
    // 获取资源列表
    const items = await getItems(event.queryStringParameters);
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(items)
    };
  }
}

// Serverless Framework 配置
/*
# serverless.yml
service: my-serverless-api

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  environment:
    DYNAMODB_TABLE: \${self:service}-\${opt:stage, self:provider.stage}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource: "arn:aws:dynamodb:\${opt:region, self:provider.region}:*:table/\${self:provider.environment.DYNAMODB_TABLE}"

functions:
  api:
    handler: handler.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true

resources:
  Resources:
    TodosDynamoDbTable:
      Type: 'AWS::DynamoDB::Table'
      Properties:
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: \${self:provider.environment.DYNAMODB_TABLE}
*/

// Vercel 无服务器函数
// api/users.js
export default async function handler(req, res) {
  const { method, query, body } = req;

  // 设置CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    switch (method) {
      case 'GET':
        const users = await getUsers(query);
        res.status(200).json(users);
        break;

      case 'POST':
        const newUser = await createUser(body);
        res.status(201).json(newUser);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(\`Method \${method} Not Allowed\`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}

// Netlify Functions
// netlify/functions/api.js
const { MongoClient } = require('mongodb');

exports.handler = async (event, context) => {
  // 只在冷启动时连接数据库
  if (!context.clientContext.custom.dbClient) {
    context.clientContext.custom.dbClient = new MongoClient(process.env.MONGODB_URI);
    await context.clientContext.custom.dbClient.connect();
  }

  const { httpMethod, path, body } = event;

  try {
    const db = context.clientContext.custom.dbClient.db('myapp');

    switch (httpMethod) {
      case 'GET':
        const items = await db.collection('items').find({}).toArray();
        return {
          statusCode: 200,
          body: JSON.stringify(items)
        };

      case 'POST':
        const newItem = JSON.parse(body);
        const result = await db.collection('items').insertOne(newItem);
        return {
          statusCode: 201,
          body: JSON.stringify({ id: result.insertedId, ...newItem })
        };

      default:
        return {
          statusCode: 405,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message })
    };
  }
};

// 事件驱动架构
// S3事件触发的Lambda函数
exports.s3Handler = async (event) => {
  for (const record of event.Records) {
    const bucket = record.s3.bucket.name;
    const key = record.s3.object.key;

    console.log(\`Processing file: \${key} from bucket: \${bucket}\`);

    // 处理上传的文件
    if (key.endsWith('.jpg') || key.endsWith('.png')) {
      await processImage(bucket, key);
    } else if (key.endsWith('.csv')) {
      await processCSV(bucket, key);
    }
  }
};

// DynamoDB Streams 触发器
exports.dynamoHandler = async (event) => {
  for (const record of event.Records) {
    const { eventName, dynamodb } = record;

    switch (eventName) {
      case 'INSERT':
        await handleInsert(dynamodb.NewImage);
        break;
      case 'MODIFY':
        await handleUpdate(dynamodb.OldImage, dynamodb.NewImage);
        break;
      case 'REMOVE':
        await handleDelete(dynamodb.OldImage);
        break;
    }
  }
};

// 定时任务
exports.cronHandler = async (event) => {
  console.log('Running scheduled task:', event.time);

  // 执行定时任务
  await cleanupOldData();
  await sendDailyReport();
  await updateCache();

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Scheduled task completed' })
  };
};`,
      useCases: ['事件处理', '微服务', 'API开发', '自动化任务'],
      relatedTerms: ['lambda', 'functions', 'event-driven', 'cloud computing']
    }
  ],

  // 开发工具与环境 (40个术语)
  devtools: [
    {
      id: 'devtools_1',
      name: 'webpack',
      chinese: 'Webpack打包工具',
      description: '现代JavaScript应用程序的静态模块打包器，将模块及其依赖打包成静态资源',
      difficulty: 'intermediate',
      tags: ['bundling', 'build-tools', 'modules'],
      example: `// webpack.config.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  mode: 'development', // 或 'production'

  entry: {
    main: './src/index.js',
    vendor: './src/vendor.js'
  },

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    clean: true
  },

  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader'
        ]
      },
      {
        test: /\.scss$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'sass-loader'
        ]
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: 'asset/resource',
        generator: {
          filename: 'images/[name].[hash][ext]'
        }
      }
    ]
  },

  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html',
      filename: 'index.html'
    }),
    new MiniCssExtractPlugin({
      filename: '[name].[contenthash].css'
    })
  ],

  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  },

  devServer: {
    contentBase: path.join(__dirname, 'dist'),
    port: 3000,
    hot: true,
    open: true
  }
};

// package.json scripts
{
  "scripts": {
    "dev": "webpack serve --mode development",
    "build": "webpack --mode production",
    "analyze": "webpack-bundle-analyzer dist/main.js"
  }
}`,
      useCases: ['模块打包', '代码分割', '资源优化', '开发服务器'],
      relatedTerms: ['babel', 'loaders', 'plugins', 'bundling']
    },
    {
      id: 'devtools_2',
      name: 'git_version_control',
      chinese: 'Git版本控制',
      description: '分布式版本控制系统，用于跟踪代码变更和团队协作',
      difficulty: 'beginner',
      tags: ['git', 'version-control', 'collaboration'],
      example: `// Git 基本命令和工作流

// 1. 仓库初始化和配置
/*
git init                          # 初始化新仓库
git clone <url>                   # 克隆远程仓库
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --list                # 查看配置
*/

// 2. 基本操作
/*
git status                        # 查看工作区状态
git add <file>                    # 添加文件到暂存区
git add .                         # 添加所有文件
git add -A                        # 添加所有变更
git commit -m "commit message"    # 提交变更
git commit -am "message"          # 添加并提交已跟踪文件
git log                           # 查看提交历史
git log --oneline                 # 简洁的提交历史
git log --graph --all             # 图形化显示分支
*/

// 3. 分支管理
/*
git branch                        # 查看本地分支
git branch -a                     # 查看所有分支
git branch <branch-name>          # 创建新分支
git checkout <branch-name>        # 切换分支
git checkout -b <branch-name>     # 创建并切换分支
git merge <branch-name>           # 合并分支
git branch -d <branch-name>       # 删除分支
git branch -D <branch-name>       # 强制删除分支
*/

// 4. 远程仓库操作
/*
git remote -v                     # 查看远程仓库
git remote add origin <url>       # 添加远程仓库
git push origin <branch>          # 推送到远程分支
git push -u origin main           # 推送并设置上游分支
git pull origin <branch>          # 拉取远程分支
git fetch origin                  # 获取远程更新
git push --tags                   # 推送标签
*/

// 5. 高级操作
/*
git stash                         # 暂存当前工作
git stash pop                     # 恢复暂存的工作
git stash list                    # 查看暂存列表
git reset --soft HEAD~1          # 软重置到上一个提交
git reset --hard HEAD~1          # 硬重置到上一个提交
git revert <commit-hash>          # 撤销指定提交
git cherry-pick <commit-hash>     # 挑选提交
git rebase <branch>               # 变基操作
git rebase -i HEAD~3              # 交互式变基
*/

// Git工作流示例

// Feature Branch 工作流
/*
# 1. 从主分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/user-authentication

# 2. 开发功能
git add .
git commit -m "Add user login functionality"
git commit -m "Add password validation"
git commit -m "Add user session management"

# 3. 推送功能分支
git push -u origin feature/user-authentication

# 4. 创建Pull Request/Merge Request
# (在GitHub/GitLab等平台上操作)

# 5. 合并后清理
git checkout main
git pull origin main
git branch -d feature/user-authentication
*/

// Gitflow 工作流
/*
# 初始化gitflow
git flow init

# 开始新功能
git flow feature start user-profile

# 完成功能
git flow feature finish user-profile

# 开始发布
git flow release start v1.2.0

# 完成发布
git flow release finish v1.2.0

# 紧急修复
git flow hotfix start critical-bug
git flow hotfix finish critical-bug
*/

// .gitignore 文件示例
/*
# 依赖目录
node_modules/
vendor/

# 构建输出
dist/
build/
*.min.js
*.min.css

# 环境配置
.env
.env.local
.env.production

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
*.tmp

# 数据库文件
*.sqlite
*.db

# 编译文件
*.o
*.so
*.dylib
*.exe
*/

// Git Hooks 示例
// .git/hooks/pre-commit
/*
#!/bin/sh
# 提交前运行测试和代码检查

echo "Running pre-commit checks..."

# 运行ESLint
npm run lint
if [ $? -ne 0 ]; then
  echo "ESLint failed. Please fix the issues before committing."
  exit 1
fi

# 运行测试
npm test
if [ $? -ne 0 ]; then
  echo "Tests failed. Please fix the issues before committing."
  exit 1
fi

# 检查提交信息格式
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'
if ! grep -qE "$commit_regex" "$1"; then
  echo "Invalid commit message format. Please use: type(scope): description"
  exit 1
fi

echo "Pre-commit checks passed!"
*/

// 使用Husky管理Git Hooks
// package.json
/*
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS",
      "pre-push": "npm test"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{css,scss,less}": [
      "stylelint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
*/

// 提交信息规范 (Conventional Commits)
/*
feat: add user authentication system
fix: resolve memory leak in data processing
docs: update API documentation
style: format code with prettier
refactor: restructure user service
test: add unit tests for payment module
chore: update dependencies

# 带作用域
feat(auth): implement OAuth2 integration
fix(ui): correct button alignment issue
docs(api): add endpoint documentation

# 破坏性变更
feat!: change API response format
feat(api)!: remove deprecated endpoints

BREAKING CHANGE: The API response format has changed.
Old format: { data: {...} }
New format: { result: {...}, meta: {...} }
*/`,
      useCases: ['版本控制', '团队协作', '代码历史', '分支管理'],
      relatedTerms: ['version control', 'branching', 'merging', 'collaboration']
    },
    {
      id: 'devtools_3',
      name: 'docker_containerization',
      chinese: 'Docker容器化',
      description: '轻量级虚拟化技术，将应用及其依赖打包到可移植的容器中',
      difficulty: 'intermediate',
      tags: ['docker', 'containerization', 'deployment'],
      example: `// Dockerfile 示例
/*
# 多阶段构建的Node.js应用
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动命令
CMD ["npm", "start"]
*/

// Docker Compose 配置
/*
version: '3.8'

services:
  # Web应用
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/myapp
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # 数据库
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
*/

// Docker 常用命令
/*
# 镜像操作
docker build -t myapp:latest .          # 构建镜像
docker images                           # 查看镜像列表
docker rmi <image-id>                   # 删除镜像
docker pull nginx:alpine                # 拉取镜像
docker push myapp:latest                # 推送镜像

# 容器操作
docker run -d -p 3000:3000 myapp:latest # 运行容器
docker ps                               # 查看运行中的容器
docker ps -a                            # 查看所有容器
docker stop <container-id>              # 停止容器
docker start <container-id>             # 启动容器
docker restart <container-id>           # 重启容器
docker rm <container-id>                # 删除容器
docker logs <container-id>              # 查看容器日志
docker exec -it <container-id> /bin/sh  # 进入容器

# Docker Compose操作
docker-compose up -d                    # 启动服务
docker-compose down                     # 停止服务
docker-compose logs                     # 查看日志
docker-compose ps                       # 查看服务状态
docker-compose build                    # 构建服务
docker-compose pull                     # 拉取镜像

# 清理操作
docker system prune                     # 清理未使用的资源
docker volume prune                     # 清理未使用的卷
docker network prune                    # 清理未使用的网络
*/

// .dockerignore 文件
/*
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.vscode
.idea
*.log
dist
build
.DS_Store
Thumbs.db
*/

// 生产环境优化
/*
# 多阶段构建优化
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["npm", "start"]
*/

// Kubernetes部署配置
/*
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: myapp:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: myapp-service
spec:
  selector:
    app: myapp
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
*/`,
      useCases: ['应用部署', '环境一致性', '微服务', '持续集成'],
      relatedTerms: ['containerization', 'orchestration', 'kubernetes', 'deployment']
    }
  ],

  // 测试与质量保证 (35个术语)
  testing: [
    {
      id: 'testing_1',
      name: 'unit_testing',
      chinese: '单元测试',
      description: '对软件中最小可测试单元进行检查和验证的测试方法',
      difficulty: 'beginner',
      tags: ['testing', 'quality', 'automation'],
      example: `// Jest单元测试示例
// math.js
function add(a, b) {
  return a + b;
}

function multiply(a, b) {
  return a * b;
}

function divide(a, b) {
  if (b === 0) {
    throw new Error('Division by zero');
  }
  return a / b;
}

module.exports = { add, multiply, divide };

// math.test.js
const { add, multiply, divide } = require('./math');

describe('Math functions', () => {
  describe('add', () => {
    test('should add two positive numbers', () => {
      expect(add(2, 3)).toBe(5);
    });

    test('should add negative numbers', () => {
      expect(add(-2, -3)).toBe(-5);
    });

    test('should handle zero', () => {
      expect(add(5, 0)).toBe(5);
    });
  });

  describe('multiply', () => {
    test('should multiply two numbers', () => {
      expect(multiply(3, 4)).toBe(12);
    });

    test('should handle zero multiplication', () => {
      expect(multiply(5, 0)).toBe(0);
    });
  });

  describe('divide', () => {
    test('should divide two numbers', () => {
      expect(divide(10, 2)).toBe(5);
    });

    test('should throw error when dividing by zero', () => {
      expect(() => divide(10, 0)).toThrow('Division by zero');
    });
  });
});

// React组件测试
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Counter from './Counter';

describe('Counter Component', () => {
  test('renders initial count', () => {
    render(<Counter initialCount={0} />);
    expect(screen.getByText('Count: 0')).toBeInTheDocument();
  });

  test('increments count when button is clicked', () => {
    render(<Counter initialCount={0} />);
    const incrementButton = screen.getByText('Increment');

    fireEvent.click(incrementButton);
    expect(screen.getByText('Count: 1')).toBeInTheDocument();
  });

  test('decrements count when button is clicked', () => {
    render(<Counter initialCount={5} />);
    const decrementButton = screen.getByText('Decrement');

    fireEvent.click(decrementButton);
    expect(screen.getByText('Count: 4')).toBeInTheDocument();
  });
});`,
      useCases: ['代码质量保证', '回归测试', '重构支持', '文档说明'],
      relatedTerms: ['integration testing', 'tdd', 'jest', 'mocking']
    },
    {
      id: 'testing_2',
      name: 'integration_testing',
      chinese: '集成测试',
      description: '测试多个组件或服务之间交互的测试方法',
      difficulty: 'intermediate',
      tags: ['testing', 'integration', 'api'],
      example: `// API集成测试示例
const request = require('supertest');
const app = require('../app');
const { setupTestDB, cleanupTestDB } = require('./helpers/database');

describe('User API Integration Tests', () => {
  let server;
  let testUser;

  beforeAll(async () => {
    await setupTestDB();
    server = app.listen(0); // 使用随机端口
  });

  afterAll(async () => {
    await cleanupTestDB();
    await server.close();
  });

  beforeEach(async () => {
    // 创建测试用户
    testUser = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User'
    };
  });

  describe('POST /api/users', () => {
    test('should create a new user', async () => {
      const response = await request(server)
        .post('/api/users')
        .send(testUser)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(Number),
        email: testUser.email,
        name: testUser.name
      });
      expect(response.body.password).toBeUndefined();
    });

    test('should return 400 for invalid email', async () => {
      const invalidUser = { ...testUser, email: 'invalid-email' };

      const response = await request(server)
        .post('/api/users')
        .send(invalidUser)
        .expect(400);

      expect(response.body.error).toContain('Invalid email');
    });

    test('should return 409 for duplicate email', async () => {
      // 先创建一个用户
      await request(server)
        .post('/api/users')
        .send(testUser)
        .expect(201);

      // 尝试创建相同邮箱的用户
      const response = await request(server)
        .post('/api/users')
        .send(testUser)
        .expect(409);

      expect(response.body.error).toContain('Email already exists');
    });
  });

  describe('Authentication Flow', () => {
    let userId;
    let authToken;

    beforeEach(async () => {
      // 创建用户
      const createResponse = await request(server)
        .post('/api/users')
        .send(testUser);

      userId = createResponse.body.id;
    });

    test('should authenticate user with valid credentials', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(response.body).toMatchObject({
        token: expect.any(String),
        user: {
          id: userId,
          email: testUser.email,
          name: testUser.name
        }
      });

      authToken = response.body.token;
    });

    test('should access protected route with valid token', async () => {
      // 先登录获取token
      const loginResponse = await request(server)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      authToken = loginResponse.body.token;

      // 访问受保护的路由
      const response = await request(server)
        .get('/api/users/profile')
        .set('Authorization', \`Bearer \${authToken}\`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: userId,
        email: testUser.email,
        name: testUser.name
      });
    });

    test('should reject access without token', async () => {
      await request(server)
        .get('/api/users/profile')
        .expect(401);
    });
  });
});

// 数据库集成测试
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const User = require('../models/User');

describe('User Model Integration Tests', () => {
  let mongoServer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await User.deleteMany({});
  });

  test('should save user to database', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    };

    const user = new User(userData);
    const savedUser = await user.save();

    expect(savedUser._id).toBeDefined();
    expect(savedUser.email).toBe(userData.email);
    expect(savedUser.createdAt).toBeDefined();
  });

  test('should enforce unique email constraint', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    };

    await new User(userData).save();

    // 尝试保存相同邮箱的用户
    const duplicateUser = new User(userData);

    await expect(duplicateUser.save()).rejects.toThrow();
  });
});

// 外部服务集成测试
const nock = require('nock');

describe('External API Integration Tests', () => {
  afterEach(() => {
    nock.cleanAll();
  });

  test('should handle successful payment processing', async () => {
    // Mock外部支付API
    nock('https://api.stripe.com')
      .post('/v1/charges')
      .reply(200, {
        id: 'ch_test_123',
        status: 'succeeded',
        amount: 2000
      });

    const paymentData = {
      amount: 2000,
      currency: 'usd',
      source: 'tok_visa'
    };

    const response = await request(server)
      .post('/api/payments')
      .send(paymentData)
      .expect(200);

    expect(response.body).toMatchObject({
      success: true,
      chargeId: 'ch_test_123'
    });
  });

  test('should handle payment API failure', async () => {
    // Mock外部API失败
    nock('https://api.stripe.com')
      .post('/v1/charges')
      .reply(400, {
        error: {
          message: 'Your card was declined.'
        }
      });

    const paymentData = {
      amount: 2000,
      currency: 'usd',
      source: 'tok_chargeDeclined'
    };

    const response = await request(server)
      .post('/api/payments')
      .send(paymentData)
      .expect(400);

    expect(response.body.error).toContain('declined');
  });
});

// 端到端测试 (E2E)
const puppeteer = require('puppeteer');

describe('E2E User Registration Flow', () => {
  let browser;
  let page;

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: process.env.CI === 'true',
      slowMo: 50
    });
  });

  afterAll(async () => {
    await browser.close();
  });

  beforeEach(async () => {
    page = await browser.newPage();
    await page.goto('http://localhost:3000');
  });

  afterEach(async () => {
    await page.close();
  });

  test('should complete user registration flow', async () => {
    // 导航到注册页面
    await page.click('[data-testid="register-link"]');
    await page.waitForSelector('[data-testid="register-form"]');

    // 填写注册表单
    await page.type('[data-testid="email-input"]', '<EMAIL>');
    await page.type('[data-testid="password-input"]', 'password123');
    await page.type('[data-testid="name-input"]', 'Test User');

    // 提交表单
    await page.click('[data-testid="register-button"]');

    // 等待重定向到仪表板
    await page.waitForNavigation();
    expect(page.url()).toContain('/dashboard');

    // 验证用户信息显示
    const welcomeMessage = await page.textContent('[data-testid="welcome-message"]');
    expect(welcomeMessage).toContain('Welcome, Test User');
  });

  test('should show validation errors for invalid input', async () => {
    await page.click('[data-testid="register-link"]');
    await page.waitForSelector('[data-testid="register-form"]');

    // 提交空表单
    await page.click('[data-testid="register-button"]');

    // 检查错误消息
    const emailError = await page.textContent('[data-testid="email-error"]');
    const passwordError = await page.textContent('[data-testid="password-error"]');

    expect(emailError).toContain('Email is required');
    expect(passwordError).toContain('Password is required');
  });
});`,
      useCases: ['API测试', '数据库测试', '服务集成', '端到端测试'],
      relatedTerms: ['api testing', 'database testing', 'mocking', 'e2e testing']
    },
    {
      id: 'testing_3',
      name: 'tdd_bdd',
      chinese: '测试驱动开发',
      description: '先写测试再写代码的开发方法，确保代码质量和需求覆盖',
      difficulty: 'intermediate',
      tags: ['tdd', 'bdd', 'methodology'],
      example: `// TDD (Test-Driven Development) 示例

// 第一步：编写失败的测试
describe('Calculator', () => {
  test('should add two numbers correctly', () => {
    const calculator = new Calculator();
    const result = calculator.add(2, 3);
    expect(result).toBe(5);
  });

  test('should subtract two numbers correctly', () => {
    const calculator = new Calculator();
    const result = calculator.subtract(5, 3);
    expect(result).toBe(2);
  });

  test('should multiply two numbers correctly', () => {
    const calculator = new Calculator();
    const result = calculator.multiply(4, 3);
    expect(result).toBe(12);
  });

  test('should divide two numbers correctly', () => {
    const calculator = new Calculator();
    const result = calculator.divide(10, 2);
    expect(result).toBe(5);
  });

  test('should throw error when dividing by zero', () => {
    const calculator = new Calculator();
    expect(() => calculator.divide(10, 0)).toThrow('Division by zero');
  });
});

// 第二步：编写最小可行代码
class Calculator {
  add(a, b) {
    return a + b;
  }

  subtract(a, b) {
    return a - b;
  }

  multiply(a, b) {
    return a * b;
  }

  divide(a, b) {
    if (b === 0) {
      throw new Error('Division by zero');
    }
    return a / b;
  }
}

// 第三步：重构代码
class Calculator {
  constructor() {
    this.history = [];
  }

  add(a, b) {
    const result = this._validateAndCalculate(a, b, (x, y) => x + y);
    this._recordOperation('add', a, b, result);
    return result;
  }

  subtract(a, b) {
    const result = this._validateAndCalculate(a, b, (x, y) => x - y);
    this._recordOperation('subtract', a, b, result);
    return result;
  }

  multiply(a, b) {
    const result = this._validateAndCalculate(a, b, (x, y) => x * y);
    this._recordOperation('multiply', a, b, result);
    return result;
  }

  divide(a, b) {
    if (b === 0) {
      throw new Error('Division by zero');
    }
    const result = this._validateAndCalculate(a, b, (x, y) => x / y);
    this._recordOperation('divide', a, b, result);
    return result;
  }

  getHistory() {
    return this.history;
  }

  clear() {
    this.history = [];
  }

  _validateAndCalculate(a, b, operation) {
    if (typeof a !== 'number' || typeof b !== 'number') {
      throw new Error('Both arguments must be numbers');
    }
    return operation(a, b);
  }

  _recordOperation(operation, a, b, result) {
    this.history.push({
      operation,
      operands: [a, b],
      result,
      timestamp: new Date()
    });
  }
}

// BDD (Behavior-Driven Development) 示例
// 使用Cucumber/Gherkin语法

/*
Feature: User Authentication
  As a user
  I want to be able to log in to the system
  So that I can access my personal dashboard

  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter valid password "password123"
    And I click the login button
    Then I should be redirected to the dashboard
    And I should see a welcome message

  Scenario: Failed login with invalid credentials
    Given I am on the login page
    When I enter invalid email "<EMAIL>"
    And I enter invalid password "wrongpassword"
    And I click the login button
    Then I should see an error message "Invalid credentials"
    And I should remain on the login page

  Scenario: Login form validation
    Given I am on the login page
    When I leave the email field empty
    And I leave the password field empty
    And I click the login button
    Then I should see validation errors
    And the login button should be disabled
*/

// BDD步骤定义 (Step Definitions)
const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('@playwright/test');

Given('I am on the login page', async function () {
  await this.page.goto('/login');
});

When('I enter valid email {string}', async function (email) {
  await this.page.fill('[data-testid="email-input"]', email);
});

When('I enter valid password {string}', async function (password) {
  await this.page.fill('[data-testid="password-input"]', password);
});

When('I click the login button', async function () {
  await this.page.click('[data-testid="login-button"]');
});

Then('I should be redirected to the dashboard', async function () {
  await this.page.waitForURL('/dashboard');
  expect(this.page.url()).toContain('/dashboard');
});

Then('I should see a welcome message', async function () {
  const welcomeMessage = await this.page.textContent('[data-testid="welcome-message"]');
  expect(welcomeMessage).toContain('Welcome');
});

// Jest BDD风格测试
describe('User Management System', () => {
  describe('Given a new user wants to register', () => {
    describe('When they provide valid information', () => {
      test('Then they should be successfully registered', async () => {
        const userService = new UserService();
        const userData = {
          email: '<EMAIL>',
          password: 'securepassword',
          name: 'New User'
        };

        const result = await userService.register(userData);

        expect(result.success).toBe(true);
        expect(result.user.email).toBe(userData.email);
        expect(result.user.id).toBeDefined();
      });
    });

    describe('When they provide invalid email', () => {
      test('Then registration should fail with validation error', async () => {
        const userService = new UserService();
        const userData = {
          email: 'invalid-email',
          password: 'securepassword',
          name: 'New User'
        };

        await expect(userService.register(userData))
          .rejects.toThrow('Invalid email format');
      });
    });
  });

  describe('Given an existing user wants to login', () => {
    let existingUser;

    beforeEach(async () => {
      const userService = new UserService();
      existingUser = await userService.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Existing User'
      });
    });

    describe('When they provide correct credentials', () => {
      test('Then they should be successfully authenticated', async () => {
        const authService = new AuthService();

        const result = await authService.login(
          '<EMAIL>',
          'password123'
        );

        expect(result.success).toBe(true);
        expect(result.token).toBeDefined();
        expect(result.user.email).toBe('<EMAIL>');
      });
    });

    describe('When they provide incorrect password', () => {
      test('Then authentication should fail', async () => {
        const authService = new AuthService();

        await expect(authService.login(
          '<EMAIL>',
          'wrongpassword'
        )).rejects.toThrow('Invalid credentials');
      });
    });
  });
});

// 测试金字塔实现
// 1. 单元测试 (70%)
describe('Unit Tests - Password Validator', () => {
  test('should validate strong password', () => {
    expect(PasswordValidator.isStrong('StrongP@ss123')).toBe(true);
  });

  test('should reject weak password', () => {
    expect(PasswordValidator.isStrong('weak')).toBe(false);
  });
});

// 2. 集成测试 (20%)
describe('Integration Tests - User Registration', () => {
  test('should register user and send welcome email', async () => {
    const result = await userService.register(validUserData);
    expect(result.success).toBe(true);
    expect(emailService.sendWelcomeEmail).toHaveBeenCalled();
  });
});

// 3. E2E测试 (10%)
describe('E2E Tests - Complete User Journey', () => {
  test('should complete full registration and login flow', async () => {
    await page.goto('/register');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'StrongP@ss123');
    await page.click('[data-testid="register-button"]');

    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');
  });
});`,
      useCases: ['质量保证', '需求验证', '重构安全', '文档化'],
      relatedTerms: ['red-green-refactor', 'behavior specification', 'acceptance criteria', 'test pyramid']
    }
  ],

  // TypeScript (30个术语)
  typescript: [
    {
      id: 'ts_1',
      name: 'type_annotations',
      chinese: '类型注解',
      description: 'TypeScript中为变量、函数参数和返回值指定类型的语法',
      difficulty: 'beginner',
      tags: ['types', 'annotations', 'static-typing'],
      example: `// 基本类型注解
let name: string = 'Alice';
let age: number = 25;
let isActive: boolean = true;
let items: string[] = ['apple', 'banana', 'orange'];
let coordinates: [number, number] = [10, 20];

// 函数类型注解
function greet(name: string): string {
  return \`Hello, \${name}!\`;
}

function add(a: number, b: number): number {
  return a + b;
}

function processUser(user: { name: string; age: number }): void {
  console.log(\`Processing user: \${user.name}, age: \${user.age}\`);
}

// 可选参数和默认参数
function createUser(name: string, age?: number, isAdmin: boolean = false): User {
  return {
    name,
    age: age || 0,
    isAdmin
  };
}

// 联合类型
let id: string | number = 'user123';
id = 42; // 也是有效的

function formatId(id: string | number): string {
  if (typeof id === 'string') {
    return id.toUpperCase();
  }
  return id.toString();
}

// 数组类型的不同写法
let numbers1: number[] = [1, 2, 3];
let numbers2: Array<number> = [1, 2, 3];

// 对象类型
let user: {
  name: string;
  age: number;
  email?: string; // 可选属性
  readonly id: number; // 只读属性
} = {
  name: 'Alice',
  age: 25,
  id: 1
};

// 函数类型
let calculate: (a: number, b: number) => number;
calculate = (x, y) => x + y;

// 泛型函数
function identity<T>(arg: T): T {
  return arg;
}

let stringIdentity = identity<string>('hello');
let numberIdentity = identity<number>(42);

// 类型断言
let someValue: unknown = 'this is a string';
let strLength: number = (someValue as string).length;
// 或者使用尖括号语法（在JSX中不推荐）
let strLength2: number = (<string>someValue).length;`,
      useCases: ['类型安全', '代码提示', '错误检查', '重构支持'],
      relatedTerms: ['static typing', 'type checking', 'intellisense', 'compile time']
    },
    {
      id: 'ts_2',
      name: 'interfaces',
      chinese: '接口',
      description: '定义对象结构和契约的TypeScript特性，用于类型检查和代码组织',
      difficulty: 'beginner',
      tags: ['interfaces', 'contracts', 'structure'],
      example: `// 基本接口定义
interface User {
  id: number;
  name: string;
  email: string;
  age?: number; // 可选属性
  readonly createdAt: Date; // 只读属性
}

// 使用接口
const user: User = {
  id: 1,
  name: 'Alice',
  email: '<EMAIL>',
  createdAt: new Date()
};

// 函数接口
interface CalculatorFunction {
  (a: number, b: number): number;
}

const add: CalculatorFunction = (x, y) => x + y;
const multiply: CalculatorFunction = (x, y) => x * y;

// 方法接口
interface UserService {
  getUser(id: number): Promise<User>;
  createUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User>;
  deleteUser(id: number): Promise<void>;
}

// 实现接口
class DatabaseUserService implements UserService {
  async getUser(id: number): Promise<User> {
    // 数据库查询逻辑
    const userData = await db.users.findById(id);
    return userData;
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<User> {
    const newUser = {
      ...userData,
      id: generateId(),
      createdAt: new Date()
    };
    await db.users.insert(newUser);
    return newUser;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User> {
    await db.users.update(id, updates);
    return this.getUser(id);
  }

  async deleteUser(id: number): Promise<void> {
    await db.users.delete(id);
  }
}

// 接口继承
interface Animal {
  name: string;
  age: number;
}

interface Dog extends Animal {
  breed: string;
  bark(): void;
}

interface Cat extends Animal {
  color: string;
  meow(): void;
}

// 多重继承
interface Pet extends Animal {
  owner: string;
}

interface ServiceDog extends Dog, Pet {
  serviceType: string;
  isWorking: boolean;
}

// 索引签名
interface StringDictionary {
  [key: string]: string;
}

interface NumberDictionary {
  [key: string]: number;
  length: number; // 可以有具体的属性
}

// 泛型接口
interface Repository<T> {
  findById(id: number): Promise<T | null>;
  findAll(): Promise<T[]>;
  create(entity: Omit<T, 'id'>): Promise<T>;
  update(id: number, updates: Partial<T>): Promise<T>;
  delete(id: number): Promise<void>;
}

interface Product {
  id: number;
  name: string;
  price: number;
  category: string;
}

class ProductRepository implements Repository<Product> {
  async findById(id: number): Promise<Product | null> {
    return await db.products.findById(id);
  }

  async findAll(): Promise<Product[]> {
    return await db.products.findAll();
  }

  async create(productData: Omit<Product, 'id'>): Promise<Product> {
    const product = { ...productData, id: generateId() };
    await db.products.insert(product);
    return product;
  }

  async update(id: number, updates: Partial<Product>): Promise<Product> {
    await db.products.update(id, updates);
    return this.findById(id)!;
  }

  async delete(id: number): Promise<void> {
    await db.products.delete(id);
  }
}

// 条件类型接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

// 使用示例
const userResponse: ApiResponse<User> = {
  success: true,
  data: user
};

const usersResponse: PaginatedResponse<User> = {
  success: true,
  data: [user],
  pagination: {
    page: 1,
    limit: 10,
    total: 100
  }
};`,
      useCases: ['契约定义', '类型约束', '代码组织', 'API设计'],
      relatedTerms: ['contracts', 'type definitions', 'inheritance', 'polymorphism']
    }
  ],

  // 性能优化 (25个术语)
  performance: [
    {
      id: 'perf_1',
      name: 'lazy_loading',
      chinese: '懒加载',
      description: '延迟加载资源直到真正需要时才加载，提高初始页面加载性能',
      difficulty: 'intermediate',
      tags: ['performance', 'optimization', 'loading'],
      example: `// 图片懒加载
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target;
      img.src = img.dataset.src;
      observer.unobserve(img);
    }
  });
});

// 观察所有懒加载图片
document.querySelectorAll('img[data-src]').forEach(img => {
  observer.observe(img);
});

// React组件懒加载
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./Component'));

function App() {
  return (
    <Suspense fallback="Loading...">
      <LazyComponent />
    </Suspense>
  );
}`,
      useCases: ['页面性能', '资源优化', '用户体验', '带宽节省'],
      relatedTerms: ['code splitting', 'intersection observer', 'dynamic imports', 'virtual scrolling']
    }
  ],

  // Python编程 (40个术语)
  python: [
    {
      id: 'py_1',
      name: 'list_comprehension',
      chinese: '列表推导式',
      description: 'Python中创建列表的简洁语法，可以在一行代码中完成过滤和转换操作',
      difficulty: 'intermediate',
      tags: ['syntax', 'functional', 'lists'],
      example: `# 基本列表推导式
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

# 创建平方数列表
squares = [x**2 for x in numbers]
print(squares)  # [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]

# 过滤偶数并求平方
even_squares = [x**2 for x in numbers if x % 2 == 0]
print(even_squares)  # [4, 16, 36, 64, 100]

# 嵌套列表推导式
matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
flattened = [num for row in matrix for num in row]
print(flattened)  # [1, 2, 3, 4, 5, 6, 7, 8, 9]

# 条件表达式
result = [x if x > 0 else 0 for x in [-2, -1, 0, 1, 2]]
print(result)  # [0, 0, 0, 1, 2]

# 字典推导式
words = ['hello', 'world', 'python', 'programming']
word_lengths = {word: len(word) for word in words}
print(word_lengths)  # {'hello': 5, 'world': 5, 'python': 6, 'programming': 11}

# 集合推导式
unique_lengths = {len(word) for word in words}
print(unique_lengths)  # {5, 6, 11}

# 生成器表达式
squares_gen = (x**2 for x in numbers)
print(list(squares_gen))  # [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]

# 复杂示例：处理文件数据
def process_log_file(filename):
    with open(filename, 'r') as file:
        # 提取所有ERROR级别的日志行，并获取时间戳
        error_timestamps = [
            line.split()[0]
            for line in file
            if 'ERROR' in line and line.strip()
        ]
    return error_timestamps

# 实际应用：数据处理
students = [
    {'name': 'Alice', 'grades': [85, 90, 78]},
    {'name': 'Bob', 'grades': [92, 88, 84]},
    {'name': 'Charlie', 'grades': [76, 81, 79]}
]

# 计算每个学生的平均分
averages = [
    {
        'name': student['name'],
        'average': sum(student['grades']) / len(student['grades'])
    }
    for student in students
]

# 找出平均分大于80的学生
high_performers = [
    student['name']
    for student in students
    if sum(student['grades']) / len(student['grades']) > 80
]`,
      useCases: ['数据处理', '函数式编程', '代码简化', '性能优化'],
      relatedTerms: ['generator expressions', 'functional programming', 'iterators', 'lambda functions']
    },
    {
      id: 'py_2',
      name: 'decorators',
      chinese: '装饰器',
      description: 'Python中修改或扩展函数行为的语法糖，实现横切关注点的分离',
      difficulty: 'advanced',
      tags: ['decorators', 'metaprogramming', 'functions'],
      example: `import functools
import time

# 基本装饰器
def timer(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f}s")
        return result
    return wrapper

@timer
def slow_function():
    time.sleep(1)
    return "Done"

# 带参数的装饰器
def retry(max_attempts=3):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise e
                    print(f"Attempt {attempt + 1} failed")
        return wrapper
    return decorator

@retry(max_attempts=3)
def unreliable_function():
    import random
    if random.random() < 0.7:
        raise Exception("Random failure")
    return "Success"

# 属性装饰器
class Circle:
    def __init__(self, radius):
        self._radius = radius

    @property
    def radius(self):
        return self._radius

    @radius.setter
    def radius(self, value):
        if value < 0:
            raise ValueError("Radius cannot be negative")
        self._radius = value

    @property
    def area(self):
        return 3.14159 * self._radius ** 2`,
      useCases: ['横切关注点', '代码复用', '功能增强', '元编程'],
      relatedTerms: ['metaprogramming', 'aspect-oriented programming', 'higher-order functions', 'closures']
    }
  ],

  // 移动开发 (30个术语)
  mobile: [
    {
      id: 'mobile_1',
      name: 'react_native',
      chinese: 'React Native',
      description: '使用React开发原生移动应用的框架，支持iOS和Android平台',
      difficulty: 'intermediate',
      tags: ['mobile', 'cross-platform', 'react'],
      example: `// React Native 基础组件
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Platform,
  Dimensions
} from 'react-native';

// 基础组件示例
const UserProfile = ({ user }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleUpdateProfile = async () => {
    setIsLoading(true);
    try {
      await updateUserProfile(user.id, updatedData);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{user.name}</Text>
      <Text style={styles.subtitle}>{user.email}</Text>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={handleUpdateProfile}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Updating...' : 'Update Profile'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

// 列表组件
const UserList = ({ users }) => {
  const renderUser = ({ item }) => (
    <TouchableOpacity style={styles.userItem}>
      <Text style={styles.userName}>{item.name}</Text>
      <Text style={styles.userEmail}>{item.email}</Text>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={users}
      renderItem={renderUser}
      keyExtractor={item => item.id.toString()}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
    />
  );
};

// 响应式样式
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  userItem: {
    backgroundColor: 'white',
    padding: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  listContainer: {
    paddingVertical: 8,
  },
});

// 平台特定代码
const PlatformSpecificComponent = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {Platform.OS === 'ios' ? 'iOS App' : 'Android App'}
      </Text>

      {Platform.select({
        ios: <Text>This is iOS specific content</Text>,
        android: <Text>This is Android specific content</Text>,
      })}
    </View>
  );
};

// 导航配置
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

function HomeStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Home"
        component={HomeScreen}
        options={{ title: 'Welcome' }}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'User Profile' }}
      />
    </Stack.Navigator>
  );
}

function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator>
        <Tab.Screen
          name="HomeTab"
          component={HomeStack}
          options={{
            tabBarLabel: 'Home',
            tabBarIcon: ({ color, size }) => (
              <Icon name="home" color={color} size={size} />
            ),
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarLabel: 'Settings',
            tabBarIcon: ({ color, size }) => (
              <Icon name="settings" color={color} size={size} />
            ),
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}

// 原生模块集成
import { NativeModules, NativeEventEmitter } from 'react-native';

const { CustomNativeModule } = NativeModules;

// 调用原生方法
const callNativeMethod = async () => {
  try {
    const result = await CustomNativeModule.performNativeOperation();
    console.log('Native result:', result);
  } catch (error) {
    console.error('Native method failed:', error);
  }
};

// 监听原生事件
const eventEmitter = new NativeEventEmitter(CustomNativeModule);
const subscription = eventEmitter.addListener('CustomEvent', (event) => {
  console.log('Received native event:', event);
});

// 清理订阅
useEffect(() => {
  return () => {
    subscription.remove();
  };
}, []);`,
      useCases: ['跨平台开发', '原生性能', '代码复用', '快速开发'],
      relatedTerms: ['cross-platform', 'native modules', 'bridge', 'metro bundler']
    }
  ],

  // 云计算 (35个术语)
  cloud: [
    {
      id: 'cloud_1',
      name: 'aws_lambda',
      chinese: 'AWS Lambda',
      description: 'Amazon的无服务器计算服务，按需执行代码而无需管理服务器',
      difficulty: 'intermediate',
      tags: ['serverless', 'aws', 'functions'],
      example: `// AWS Lambda 函数示例
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

// 基本Lambda函数
exports.handler = async (event, context) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  try {
    const result = await processEvent(event);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: true,
        data: result
      })
    };
  } catch (error) {
    console.error('Error:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// API Gateway集成
exports.apiHandler = async (event, context) => {
  const { httpMethod, path, pathParameters, queryStringParameters, body } = event;

  switch (httpMethod) {
    case 'GET':
      if (pathParameters && pathParameters.id) {
        return await getItem(pathParameters.id);
      } else {
        return await getItems(queryStringParameters);
      }

    case 'POST':
      return await createItem(JSON.parse(body));

    case 'PUT':
      return await updateItem(pathParameters.id, JSON.parse(body));

    case 'DELETE':
      return await deleteItem(pathParameters.id);

    default:
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
  }
};

// DynamoDB操作
async function getItem(id) {
  const params = {
    TableName: process.env.TABLE_NAME,
    Key: { id }
  };

  const result = await dynamodb.get(params).promise();

  return {
    statusCode: result.Item ? 200 : 404,
    body: JSON.stringify(result.Item || { error: 'Item not found' })
  };
}

async function createItem(item) {
  const params = {
    TableName: process.env.TABLE_NAME,
    Item: {
      ...item,
      id: generateId(),
      createdAt: new Date().toISOString()
    }
  };

  await dynamodb.put(params).promise();

  return {
    statusCode: 201,
    body: JSON.stringify(params.Item)
  };
}

// S3事件处理
exports.s3Handler = async (event, context) => {
  const s3 = new AWS.S3();

  for (const record of event.Records) {
    const bucket = record.s3.bucket.name;
    const key = record.s3.object.key;

    console.log(\`Processing file: \${key} from bucket: \${bucket}\`);

    try {
      // 获取文件
      const object = await s3.getObject({ Bucket: bucket, Key: key }).promise();

      // 处理文件内容
      if (key.endsWith('.json')) {
        const data = JSON.parse(object.Body.toString());
        await processJsonData(data);
      } else if (key.endsWith('.csv')) {
        const csvData = object.Body.toString();
        await processCsvData(csvData);
      }

      // 移动到处理完成的文件夹
      await s3.copyObject({
        Bucket: bucket,
        CopySource: \`\${bucket}/\${key}\`,
        Key: \`processed/\${key}\`
      }).promise();

      await s3.deleteObject({ Bucket: bucket, Key: key }).promise();

    } catch (error) {
      console.error(\`Error processing \${key}:\`, error);

      // 移动到错误文件夹
      await s3.copyObject({
        Bucket: bucket,
        CopySource: \`\${bucket}/\${key}\`,
        Key: \`errors/\${key}\`
      }).promise();
    }
  }
};

// CloudWatch定时任务
exports.scheduledHandler = async (event, context) => {
  console.log('Running scheduled task:', event);

  try {
    // 清理过期数据
    await cleanupExpiredData();

    // 生成报告
    await generateDailyReport();

    // 发送通知
    await sendNotification('Daily tasks completed successfully');

  } catch (error) {
    console.error('Scheduled task failed:', error);
    await sendNotification(\`Daily tasks failed: \${error.message}\`);
  }
};

// SQS消息处理
exports.sqsHandler = async (event, context) => {
  const sqs = new AWS.SQS();

  for (const record of event.Records) {
    const messageBody = JSON.parse(record.body);

    try {
      await processMessage(messageBody);

      // 删除消息
      await sqs.deleteMessage({
        QueueUrl: process.env.QUEUE_URL,
        ReceiptHandle: record.receiptHandle
      }).promise();

    } catch (error) {
      console.error('Message processing failed:', error);

      // 发送到死信队列
      await sqs.sendMessage({
        QueueUrl: process.env.DLQ_URL,
        MessageBody: JSON.stringify({
          originalMessage: messageBody,
          error: error.message,
          timestamp: new Date().toISOString()
        })
      }).promise();
    }
  }
};

// 环境变量和配置
const config = {
  tableName: process.env.TABLE_NAME,
  bucketName: process.env.BUCKET_NAME,
  queueUrl: process.env.QUEUE_URL,
  region: process.env.AWS_REGION || 'us-east-1'
};

// 错误处理和重试
async function withRetry(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;

      const delay = Math.pow(2, i) * 1000; // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// 使用示例
async function processEvent(event) {
  return await withRetry(async () => {
    // 处理逻辑
    const result = await someAsyncOperation(event);
    return result;
  });
}`,
      useCases: ['事件处理', '微服务', 'API后端', '数据处理'],
      relatedTerms: ['serverless', 'event-driven', 'microservices', 'auto-scaling']
    }
  ],

  // DevOps与部署 (30个术语)
  devops: [
    {
      id: 'devops_1',
      name: 'ci_cd',
      chinese: '持续集成/持续部署',
      description: '自动化软件开发流程，包括代码集成、测试和部署的实践',
      difficulty: 'intermediate',
      tags: ['automation', 'deployment', 'pipeline'],
      example: `# GitHub Actions CI/CD 配置
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install and test
      run: |
        npm ci
        npm run lint
        npm run test:unit
        npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to production
      run: |
        kubectl set image deployment/myapp \\
          myapp=myregistry/myapp:latest
        kubectl rollout status deployment/myapp

# Jenkins Pipeline 示例
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                sh 'npm ci'
                sh 'npm run test'
            }
        }
        stage('Build') {
            steps {
                sh 'docker build -t myapp .'
                sh 'docker push myapp'
            }
        }
        stage('Deploy') {
            steps {
                sh 'kubectl apply -f k8s/'
            }
        }
    }
}`,
      useCases: ['自动化部署', '质量保证', '快速交付', '风险降低'],
      relatedTerms: ['automation', 'pipeline', 'deployment', 'testing']
    }
  ],

  // 数据科学 (25个术语)
  datascience: [
    {
      id: 'ds_1',
      name: 'machine_learning',
      chinese: '机器学习',
      description: '让计算机通过数据学习模式和做出预测的人工智能分支',
      difficulty: 'advanced',
      tags: ['ai', 'ml', 'algorithms'],
      example: `# Python机器学习示例
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score

# 分类任务示例
def classification_example():
    # 加载数据
    from sklearn.datasets import load_iris
    iris = load_iris()
    X, y = iris.data, iris.target

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )

    # 特征缩放
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 训练模型
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train_scaled, y_train)

    # 预测和评估
    y_pred = model.predict(X_test_scaled)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"Accuracy: {accuracy:.4f}")

    return model, scaler

# 深度学习示例
def deep_learning_example():
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout

    # 构建模型
    model = Sequential([
        Dense(512, activation='relu', input_shape=(784,)),
        Dropout(0.2),
        Dense(10, activation='softmax')
    ])

    # 编译模型
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )

    return model

# 模型部署API
from flask import Flask, request, jsonify
import joblib

app = Flask(__name__)
model = joblib.load('model.pkl')

@app.route('/predict', methods=['POST'])
def predict():
    data = request.json
    features = np.array(data['features']).reshape(1, -1)
    prediction = model.predict(features)
    return jsonify({'prediction': int(prediction[0])})`,
      useCases: ['预测分析', '模式识别', '自动化决策', '数据挖掘'],
      relatedTerms: ['supervised learning', 'unsupervised learning', 'neural networks', 'feature engineering']
    }
  ]
};

// 将所有术语合并为一个数组
const allTerms = Object.values(programmingTermsDatabase).flat();

// API路由

// 获取所有术语
app.get('/api/terms', (req, res) => {
  const { category, difficulty, search, limit = 50, offset = 0 } = req.query;
  
  let filteredTerms = [...allTerms];
  
  // 分类过滤
  if (category && category !== 'all') {
    const categoryTerms = programmingTermsDatabase[category] || [];
    filteredTerms = categoryTerms;
  }
  
  // 难度过滤
  if (difficulty && difficulty !== 'all') {
    filteredTerms = filteredTerms.filter(term => term.difficulty === difficulty);
  }
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    filteredTerms = filteredTerms.filter(term =>
      term.name.toLowerCase().includes(searchLower) ||
      term.chinese.includes(search) ||
      term.description.toLowerCase().includes(searchLower) ||
      term.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  }
  
  // 分页
  const startIndex = parseInt(offset);
  const endIndex = startIndex + parseInt(limit);
  const paginatedTerms = filteredTerms.slice(startIndex, endIndex);
  
  res.json({
    terms: paginatedTerms,
    total: filteredTerms.length,
    hasMore: endIndex < filteredTerms.length,
    categories: Object.keys(programmingTermsDatabase),
    difficulties: ['beginner', 'intermediate', 'advanced']
  });
});

// 获取单个术语详情
app.get('/api/terms/:id', (req, res) => {
  const term = allTerms.find(t => t.id === req.params.id);
  if (!term) {
    return res.status(404).json({ error: 'Term not found' });
  }
  
  // 查找相关术语
  const relatedTerms = allTerms.filter(t => 
    t.id !== term.id && 
    (t.tags.some(tag => term.tags.includes(tag)) ||
     term.relatedTerms?.some(related => t.name.includes(related)))
  ).slice(0, 5);
  
  res.json({
    ...term,
    relatedTerms: relatedTerms.map(t => ({
      id: t.id,
      name: t.name,
      chinese: t.chinese
    }))
  });
});

// 获取分类列表
app.get('/api/categories', (req, res) => {
  const categories = Object.keys(programmingTermsDatabase).map(key => ({
    id: key,
    name: key,
    count: programmingTermsDatabase[key].length,
    displayName: {
      javascript: 'JavaScript核心',
      react: 'React生态',
      nodejs: 'Node.js后端',
      database: '数据库技术',
      css: 'CSS样式',
      algorithms: '算法数据结构',
      security: 'Web安全',
      emerging: '新兴技术',
      devtools: '开发工具',
      testing: '测试质量',
      typescript: 'TypeScript',
      performance: '性能优化',
      python: 'Python编程',
      mobile: '移动开发',
      cloud: '云计算',
      devops: 'DevOps部署',
      datascience: '数据科学'
    }[key] || key
  }));
  
  res.json(categories);
});

// 获取统计信息
app.get('/api/stats', (req, res) => {
  const stats = {
    totalTerms: allTerms.length,
    categories: Object.keys(programmingTermsDatabase).length,
    byDifficulty: {
      beginner: allTerms.filter(t => t.difficulty === 'beginner').length,
      intermediate: allTerms.filter(t => t.difficulty === 'intermediate').length,
      advanced: allTerms.filter(t => t.difficulty === 'advanced').length
    },
    byCategory: Object.keys(programmingTermsDatabase).reduce((acc, key) => {
      acc[key] = programmingTermsDatabase[key].length;
      return acc;
    }, {}),
    lastUpdated: new Date().toISOString()
  };
  
  res.json(stats);
});

// 随机获取术语
app.get('/api/random', (req, res) => {
  const { count = 1 } = req.query;
  const shuffled = [...allTerms].sort(() => 0.5 - Math.random());
  const randomTerms = shuffled.slice(0, parseInt(count));
  
  res.json(randomTerms);
});

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404处理
app.use('*', (req, res) => {
  if (req.url.startsWith('/api/')) {
    res.status(404).json({ error: 'API endpoint not found' });
  } else {
    res.status(404).sendFile(path.join(__dirname, 'public', 'index.html'));
  }
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`
  🚀 终极编程术语词典服务器启动成功!
  
  📍 本地访问: http://localhost:${PORT}
  📊 API文档: http://localhost:${PORT}/api/stats
  📚 术语总数: ${allTerms.length}
  🏷️  分类数量: ${Object.keys(programmingTermsDatabase).length}
  
  💡 API端点:
     GET /api/terms - 获取术语列表
     GET /api/terms/:id - 获取术语详情
     GET /api/categories - 获取分类
     GET /api/stats - 获取统计信息
     GET /api/random - 随机术语
  
  🔥 开始探索编程世界吧!
  `);
});