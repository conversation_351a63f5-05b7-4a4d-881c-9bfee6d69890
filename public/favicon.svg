<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#grad1)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- 代码符号 -->
  <text x="32" y="42" font-family="monospace" font-size="28" font-weight="bold" text-anchor="middle" fill="white">&lt;/&gt;</text>
  
  <!-- 装饰点 -->
  <circle cx="16" cy="16" r="2" fill="rgba(255,255,255,0.6)"/>
  <circle cx="48" cy="16" r="2" fill="rgba(255,255,255,0.6)"/>
  <circle cx="16" cy="48" r="2" fill="rgba(255,255,255,0.6)"/>
  <circle cx="48" cy="48" r="2" fill="rgba(255,255,255,0.6)"/>
</svg>
